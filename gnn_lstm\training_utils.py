#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Training Utilities Module

This module provides training utilities for the GNN-LSTM financial crisis
prediction system, including:

1. EarlyStopping - Enhanced early stopping with validation gap monitoring
2. train_enhanced_ensemble - Main training function with F0.5 optimization
3. Threshold optimization for binary classification

All utilities support extended training with comprehensive monitoring and
data leakage prevention measures.
"""

from typing import Dict, List, Optional, Tuple, Union, Any

import numpy as np
import torch
import torch.nn as nn

from torch.optim import Adam, AdamW, SGD
from torch.optim.lr_scheduler import ReduceLROnPlateau, StepLR
import warnings
# Removed unused DataLoader import

from . import config
from .evaluation import calculate_f05_score
from . import data_utils

import numpy as np
import time


def safe_item(value):
    """
    Safely extract scalar value from either a tensor or already-scalar value.
    
    Args:
        value: Either a PyTorch tensor or a scalar (int, float, etc.)
        
    Returns:
        Scalar value (int, float, etc.)
    """
    if hasattr(value, 'item'):
        return value.item()
    else:
        return value


# Removed unused _validate_training_inputs function


def _validate_model_outputs(outputs: torch.Tensor, targets: torch.Tensor, 
                          step_name: str) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Comprehensive validation of model outputs and targets with NaN/infinity detection.
    Enhanced to prevent numerical instability and training collapse.
    
    Args:
        outputs: Model predictions
        targets: Ground truth labels  
        step_name: Name of current step for error reporting
        
    Returns:
        Validated and cleaned outputs and targets
        
    Raises:
        ValueError: If inputs cannot be validated or cleaned
    """
    # Basic shape validation
    if outputs.numel() == 0 or targets.numel() == 0:
        raise ValueError(f"{step_name}: Empty tensors detected")

    # SHAPE FIX: Ensure compatible shapes for loss computation
    if outputs.dim() == 2 and outputs.size(1) == 1:
        outputs = outputs.squeeze(1)  # [batch_size, 1] -> [batch_size]
    if targets.dim() == 2:
        targets = targets.squeeze(1)  # Ensure targets are 1D
    
    if outputs.shape[0] != targets.shape[0]:
        raise ValueError(f"{step_name}: Batch size mismatch: outputs {outputs.shape[0]} vs targets {targets.shape[0]}")
    
    # Enhanced NaN/infinity detection
    outputs_has_nan = torch.isnan(outputs).any()
    outputs_has_inf = torch.isinf(outputs).any()
    targets_has_nan = torch.isnan(targets).any()
    targets_has_inf = torch.isinf(targets).any()
    
    # Report and handle NaN/infinity in outputs
    if outputs_has_nan:
        nan_count = torch.isnan(outputs).sum()
        print(f"⚠️ Warning: {nan_count} NaN values detected in {step_name} outputs")
        # Replace NaN with zeros (will be handled by sigmoid)
        outputs = torch.where(torch.isnan(outputs), torch.zeros_like(outputs), outputs)
    
    if outputs_has_inf:
        inf_count = torch.isinf(outputs).sum()
        print(f"⚠️ Warning: {inf_count} infinite values detected in {step_name} outputs")
        # Clamp infinite values to reasonable range
        outputs = torch.clamp(outputs, min=-10.0, max=10.0)
    
    # Handle NaN/infinity in targets (more serious)
    if targets_has_nan:
        nan_count = torch.isnan(targets).sum()
        raise ValueError(f"{step_name}: {safe_item(nan_count)} NaN values detected in targets - data corruption!")
    
    if targets_has_inf:
        inf_count = torch.isinf(targets).sum()
        raise ValueError(f"{step_name}: {safe_item(inf_count)} infinite values detected in targets - data corruption!")
    
    # Validate target values are in expected range [0, 1]
    if torch.any(targets < 0) or torch.any(targets > 1):
        invalid_count = torch.sum((targets < 0) | (targets > 1))
        print(f"⚠️ Warning: {safe_item(invalid_count)} target values outside [0,1] range in {step_name}")
        targets = torch.clamp(targets, min=0.0, max=1.0)
    
    # Additional stability checks
    output_range = torch.max(outputs) - torch.min(outputs)
    if output_range > 50.0:  # Unusually large output range
        print(f"⚠️ Warning: Large output range ({safe_item(output_range):.2f}) in {step_name}, potential instability")
        outputs = torch.clamp(outputs, min=-15.0, max=15.0)
    
    # Check for gradient requirements (only during training, not validation)
    is_validation_step = "validation" in step_name.lower() or "val" in step_name.lower()
    if not is_validation_step and outputs.requires_grad != True:
        print(f"⚠️ Warning: Outputs don't require gradients in {step_name}")
    
    return outputs, targets


def calculate_diversity_loss(outputs: torch.Tensor, alpha: Optional[float] = None, 
                           target_var: Optional[float] = None) -> torch.Tensor:
    """
    Calculate diversity loss to encourage prediction variety.
    COMPLETELY REDESIGNED to properly encourage diversity.
    
    Args:
        outputs: Model outputs before sigmoid
        alpha: Diversity loss weight (uses config if None)
        target_var: Minimum desired output variance (uses config if None)
        
    Returns:
        Diversity loss tensor that penalizes low variance (concentrated predictions)
    """
    from . import config
    
    if alpha is None:
        alpha = getattr(config, 'DIVERSITY_LOSS_WEIGHT', 0.1)
    if target_var is None:
        # Lower target variance since we want to penalize very concentrated predictions
        target_var = getattr(config, 'MIN_OUTPUT_VARIANCE', 0.05)
    
    # Ensure all values are valid
    if alpha is None or target_var is None or alpha <= 0 or target_var <= 0:
        return torch.tensor(0.0, device=outputs.device)
    
    # Calculate output variance and standard deviation
    output_var = torch.var(outputs)
    output_std = torch.std(outputs)
    
    # REDESIGNED DIVERSITY LOSS: Penalize low variance directly
    # When variance is below target, apply penalty that decreases as variance increases
    if output_var < target_var:
        # Strong penalty for very low variance, decreases as variance approaches target
        variance_deficit = target_var - output_var
        # Use squared penalty for stronger gradient signal
        diversity_loss = alpha * (variance_deficit ** 2)
        
        # Additional penalty for extremely concentrated predictions (std < 0.01)
        if output_std < 0.01:
            extreme_penalty = alpha * 2.0 * (0.01 - output_std) ** 2
            diversity_loss = diversity_loss + extreme_penalty
            
    else:
        # Small reward for good diversity (negative loss, clamped to prevent instability)
        variance_surplus = output_var - target_var
        reward = torch.clamp(variance_surplus, max=0.1)  # Cap reward to prevent instability
        diversity_loss = -0.1 * alpha * reward
    
    # Additional diversity encouragement: penalize outputs that are too similar to each other
    if outputs.size(0) > 1:  # Only if batch size > 1
        # Calculate pairwise distances between outputs
        output_expanded = outputs.unsqueeze(1)  # [batch, 1]
        output_expanded_t = outputs.unsqueeze(0)  # [1, batch]
        pairwise_distances = torch.abs(output_expanded - output_expanded_t)  # [batch, batch]
        
        # Average pairwise distance (excluding diagonal)
        mask = ~torch.eye(outputs.size(0), dtype=torch.bool, device=outputs.device)
        avg_pairwise_distance = pairwise_distances[mask].mean()
        
        # Penalize when outputs are too similar (small pairwise distances)
        min_distance = 0.05  # Minimum desired distance between outputs
        if avg_pairwise_distance < min_distance:
            distance_penalty = alpha * 0.5 * (min_distance - avg_pairwise_distance) ** 2
            diversity_loss = diversity_loss + distance_penalty
    
    # Clamp the final result to a reasonable range
    diversity_loss = torch.clamp(diversity_loss, min=-1.0, max=10.0)
    
    # Final validation
    if torch.isinf(diversity_loss) or torch.isnan(diversity_loss):
        print(f"⚠️ Warning: Invalid diversity loss detected, using fallback")
        diversity_loss = torch.tensor(0.0, device=outputs.device)
    
    return diversity_loss


def calculate_enhanced_loss(outputs: torch.Tensor, targets: torch.Tensor, 
                          criterion: nn.Module, diversity_weight: Optional[float] = None, 
                          use_diversity: Optional[bool] = None,
                          model: Optional[nn.Module] = None) -> tuple:
    """
    Calculate enhanced loss with support for TSAP auxiliary objectives.
    
    Args:
        outputs: Model outputs (can be tuple for TSAP auxiliary loss)
        targets: Ground truth targets
        criterion: Loss function
        diversity_weight: Weight for diversity loss (optional)
        use_diversity: Whether to use diversity loss (optional)
        model: Model instance for enhanced loss computation
        
    Returns:
        Tuple of (total_loss, main_loss, aux_loss_or_diversity_loss)
    """
    # Validate criterion is a loss function, not a tensor
    if isinstance(criterion, torch.Tensor):
        raise TypeError(f"criterion must be a loss function (nn.Module), not a Tensor. Got {type(criterion)}")
    
    # Additional validation to ensure criterion is callable
    if not callable(criterion):
        raise TypeError(f"criterion must be a callable loss function, got {type(criterion)}")
    
    # Ensure criterion is an instance of nn.Module
    if not isinstance(criterion, nn.Module):
        raise TypeError(f"criterion must be an instance of nn.Module, got {type(criterion)}")
    
    # Check if model has enhanced loss computation (TSAP-Enhanced)
    if model is not None and hasattr(model, 'compute_enhanced_loss') and callable(model.compute_enhanced_loss):
        # Pass the outputs to the model's compute_enhanced_loss method
        # The model will handle unpacking if outputs is a tuple
        result = model.compute_enhanced_loss(outputs, targets, criterion)
        # Ensure result is a tuple of 3 elements
        if isinstance(result, tuple) and len(result) == 3:
            return result
        elif isinstance(result, torch.Tensor):
            # If single tensor returned, treat as total loss with no auxiliary loss
            return result, result, torch.tensor(0.0, device=result.device)
        else:
            # Fallback to standard computation
            pass
    
    # Standard enhanced loss computation for other models
    if isinstance(outputs, tuple):
        # Handle tuple outputs (auxiliary predictions)
        main_outputs, aux_outputs = outputs
        if isinstance(criterion, torch.Tensor):
            raise TypeError(f"criterion must be a loss function (nn.Module), not a Tensor. Got {type(criterion)}")
        main_loss = criterion(main_outputs, targets)
        aux_loss = criterion(aux_outputs, targets)
        # Default auxiliary weight
        aux_weight = 0.3
        total_loss = (1 - aux_weight) * main_loss + aux_weight * aux_loss
        return total_loss, main_loss, aux_loss
    
    # Standard loss computation
    main_loss = _safe_loss_computation(criterion, outputs, targets, "enhanced_loss")
    
    # Add diversity loss if enabled
    if use_diversity is None:
        use_diversity = getattr(config, 'USE_DIVERSITY_LOSS', True)
    
    if use_diversity:
        diversity_loss = calculate_diversity_loss(outputs, diversity_weight)
        total_loss = main_loss + diversity_loss
        return total_loss, main_loss, diversity_loss
    
    return main_loss, main_loss, torch.tensor(0.0, device=main_loss.device)


def _safe_loss_computation(criterion: nn.Module, outputs: torch.Tensor, 
                         targets: torch.Tensor, step_name: str, 
                         use_diversity_loss: bool = True) -> torch.Tensor:
    """Safely compute loss with validation and fallback."""
    # Validate inputs
    outputs, targets = _validate_model_outputs(outputs, targets, step_name)
    
    # Compute loss
    if use_diversity_loss:
        total_loss, base_loss, _ = calculate_enhanced_loss(outputs, targets, criterion)
    else:
        total_loss = criterion(outputs, targets)
        base_loss = total_loss  # Initialize base_loss for consistency
    
    # Validate loss
    if torch.isnan(total_loss):
        print(f"⚠️ Warning: NaN loss detected in {step_name}, using fallback loss")
        # Fallback to a simple loss computation
        total_loss = base_loss if not torch.isnan(base_loss) else torch.mean(torch.abs(outputs - targets))
        if torch.isnan(total_loss):
            print(f"⚠️ Warning: Fallback loss also NaN in {step_name}, using zero loss")
            total_loss = torch.tensor(0.0, device=outputs.device, requires_grad=True)
    
    if torch.isinf(total_loss):
        print(f"⚠️ Warning: Infinite loss detected in {step_name}, clamping")
        total_loss = torch.clamp(total_loss, max=10.0)
    
    return total_loss


class EarlyStopping:
    """
    ENHANCED Early Stopping with Robust Handling of Extreme Class Imbalance
    
    ENHANCED FEATURES:
    1. Robust handling of validation sets with very few positive samples
    2. Adaptive patience based on data characteristics
    3. Comprehensive validation gap monitoring
    4. Graceful degradation for edge cases
    5. Improved convergence detection for imbalanced datasets
    
    Monitors validation loss and F0.5 score to determine when to stop training.
    Includes validation gap monitoring to detect overfitting.
    """
    
    def __init__(
        self,
        patience: int = 35,
        min_delta: float = 1e-5,
        restore_best_weights: bool = True,
        monitor_val_gap: bool = True,
        gap_threshold: float = 0.05,
        min_epochs: int = 50,
        verbose: bool = True,
    ):
        """
        Initialize ENHANCED early stopping with imbalanced data handling.
        
        Args:
            patience: Number of epochs to wait after last improvement
            min_delta: Minimum change to qualify as an improvement
            restore_best_weights: Whether to restore best weights when stopping
            monitor_val_gap: Whether to monitor training-validation gap
            gap_threshold: Maximum allowed training-validation gap
            min_epochs: Minimum epochs before stopping is allowed
            verbose: Whether to print stopping messages
        """
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.monitor_val_gap = monitor_val_gap
        self.gap_threshold = gap_threshold
        self.min_epochs = min_epochs
        self.verbose = verbose
        
        # State variables
        self.best_val_loss = float('inf')
        self.best_val_f05 = 0.0
        self.best_weights = None
        self.patience_counter = 0
        self.stopped_epoch = 0
        self.should_stop = False
        
        # ENHANCED: Tracking for imbalanced data
        self.val_f05_history = []
        self.train_f05_history = []
        self.gap_history = []
        self.convergence_detected = False
        self.extreme_imbalance_detected = False
        
        # ENHANCED: Adaptive thresholds
        self.adaptive_patience = patience
        self.adaptive_gap_threshold = gap_threshold
    
    def __call__(
        self,
        epoch: int,
        val_loss: float,
        model: nn.Module,
        train_loss: Optional[float] = None,  # Currently unused but kept for API compatibility
        val_f05: Optional[float] = None,
        train_f05: Optional[float] = None,
    ) -> bool:
        """
        ENHANCED early stopping call with comprehensive imbalanced data handling.
        
        Args:
            epoch: Current epoch number
            val_loss: Current validation loss
            model: Model to potentially save weights from
            train_loss: Current training loss (optional)
            val_f05: Current validation F0.5 score (optional)
            train_f05: Current training F0.5 score (optional)
            
        Returns:
            True if training should stop, False otherwise
        """
        # ENHANCED: Handle None values with robust fallbacks
        if val_loss is None or np.isnan(val_loss) or np.isinf(val_loss):
            if self.verbose:
                print(f"⚠️ Warning: Invalid validation loss at epoch {epoch}, using fallback")
            val_loss = self.best_val_loss * 1.1  # Slightly worse than best
        
        if val_f05 is None or np.isnan(val_f05) or np.isinf(val_f05):
            val_f05 = 0.0
            if len(self.val_f05_history) > 0:
                val_f05 = self.val_f05_history[-1]  # Use last valid value
        
        if train_f05 is None or np.isnan(train_f05) or np.isinf(train_f05):
            train_f05 = 0.0
            if len(self.train_f05_history) > 0:
                train_f05 = self.train_f05_history[-1]  # Use last valid value
        
        # ENHANCED: Track metrics for imbalanced data analysis
        self.val_f05_history.append(val_f05)
        self.train_f05_history.append(train_f05)
        
        # ENHANCED: Detect extreme imbalance scenarios
        if epoch > 10 and not self.extreme_imbalance_detected:
            recent_val_f05 = self.val_f05_history[-10:]
            if all(f05 <= 0.01 for f05 in recent_val_f05):  # Very low F0.5 for 10 epochs
                self.extreme_imbalance_detected = True
                if self.verbose:
                    print(f"   🔍 Extreme imbalance detected - adapting early stopping strategy")
                
                # Adapt strategy for extreme imbalance
                self.adaptive_patience = min(self.patience * 2, 60)  # Increase patience
                self.adaptive_gap_threshold = min(self.gap_threshold * 2, 0.2)  # Relax gap threshold
        
        # ENHANCED: Calculate training-validation gap with robust handling
        current_gap = 0.0
        if train_f05 is not None and val_f05 is not None:
            current_gap = abs(train_f05 - val_f05)
        
        self.gap_history.append(current_gap)
        
        # ENHANCED: Primary improvement detection (prioritize F0.5 for imbalanced data)
        val_f05_improved = val_f05 is not None and val_f05 > (self.best_val_f05 + self.min_delta)
        val_loss_improved = val_loss < (self.best_val_loss - self.min_delta)
        
        # ENHANCED: Use F0.5 as primary metric for imbalanced data
        if val_f05_improved or (val_f05 is not None and val_f05 >= self.best_val_f05 and val_loss_improved):
            if val_f05 is not None:
                self.best_val_f05 = val_f05
            self.best_val_loss = val_loss
            self.patience_counter = 0
            
            # Store best weights
            if self.restore_best_weights:
                self.best_weights = {k: v.cpu().clone() for k, v in model.state_dict().items()}
            
            if self.verbose:
                if val_f05_improved:
                    print(f"   ✅ F0.5 improved: {val_f05:.4f} (epoch {epoch})")
                else:
                    print(f"   ✅ Loss improved: {val_loss:.4f} (epoch {epoch})")
        else:
            self.patience_counter += 1
        
        # ENHANCED: Check stopping conditions with adaptive thresholds
        should_stop = False
        stop_reason = ""
        
        # 1. Check minimum epochs
        if epoch < self.min_epochs:
            should_stop = False
        
        # 2. Check patience
        elif self.patience_counter >= self.adaptive_patience:
            should_stop = True
            stop_reason = f"patience ({self.adaptive_patience} epochs)"
        
        # 3. ENHANCED: Check validation gap (with adaptive threshold)
        elif self.monitor_val_gap and epoch > 20:  # Allow some epochs for stabilization
            if len(self.gap_history) >= 5:
                recent_gap = np.mean(self.gap_history[-5:])  # Average of last 5 epochs
                if recent_gap > self.adaptive_gap_threshold:
                    should_stop = True
                    stop_reason = f"validation gap ({recent_gap:.4f} > {self.adaptive_gap_threshold:.4f})"
        
        # 4. ENHANCED: Check for convergence (especially for imbalanced data)
        elif epoch > 30 and len(self.val_f05_history) >= 10:
            recent_f05 = self.val_f05_history[-10:]
            f05_std = np.std(recent_f05)
            
            if f05_std < 0.001:  # Very stable F0.5 score
                should_stop = True
                stop_reason = f"convergence (F0.5 std: {f05_std:.5f})"
                self.convergence_detected = True
        
        # ENHANCED: Apply stopping decision
        if should_stop:
            self.should_stop = True
            self.stopped_epoch = epoch
            
            if self.verbose:
                print(f"   🛑 Early stopping triggered at epoch {epoch}: {stop_reason}")
                print(f"      Best validation F0.5: {self.best_val_f05:.4f}")
                print(f"      Best validation loss: {self.best_val_loss:.4f}")
        
        # ENHANCED: Restore best weights if stopping
        if self.should_stop and self.restore_best_weights and self.best_weights is not None:
            if self.verbose:
                print("   🔄 Restoring best model weights...")
            model.load_state_dict(self.best_weights)
        
        return self.should_stop
    
    def get_summary(self) -> Dict[str, Union[float, int, bool]]:
        """
        Get ENHANCED summary of early stopping with imbalanced data insights.
        """
        summary = {
            'stopped_epoch': self.stopped_epoch,
            'best_val_loss': self.best_val_loss,
            'best_val_f05': self.best_val_f05,
            'patience_used': self.patience_counter,
            'total_patience': self.adaptive_patience,
            'stopped': self.should_stop,
            'convergence_detected': self.convergence_detected,
            'extreme_imbalance_detected': self.extreme_imbalance_detected
        }
        
        # ENHANCED: Add gap analysis
        if self.gap_history:
            summary['final_gap'] = self.gap_history[-1] if self.gap_history else 0.0
            summary['avg_gap'] = np.mean(self.gap_history)
            summary['max_gap'] = np.max(self.gap_history)
        
        # ENHANCED: Add convergence analysis
        if len(self.val_f05_history) >= 10:
            recent_f05 = self.val_f05_history[-10:]
            summary['recent_f05_std'] = np.std(recent_f05)
            summary['recent_f05_mean'] = np.mean(recent_f05)
        
        return summary


def create_optimizer(
    model: nn.Module,
    optimizer_type: str = 'adam',
    learning_rate: float = 1e-4,
    weight_decay: float = 1e-3,
    **kwargs
) -> torch.optim.Optimizer:
    """
    Create optimizer with configuration defaults.
    
    Args:
        model: Model to optimize
        optimizer_type: Type of optimizer ('adam', 'adamw', 'sgd')
        learning_rate: Learning rate
        weight_decay: Weight decay factor
        **kwargs: Additional optimizer parameters
        
    Returns:
        Configured optimizer
        
    Raises:
        ValueError: If optimizer type is not supported
    """
    if optimizer_type.lower() == 'adam':
        return Adam(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=getattr(config, 'OPTIMIZER_BETAS', (0.9, 0.999)),
            eps=getattr(config, 'OPTIMIZER_EPS', 1e-8),
            **kwargs
        )
    elif optimizer_type.lower() == 'adamw':
        return AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=getattr(config, 'OPTIMIZER_BETAS', (0.9, 0.999)),
            eps=getattr(config, 'OPTIMIZER_EPS', 1e-8),
            **kwargs
        )
    elif optimizer_type.lower() == 'sgd':
        return SGD(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            momentum=kwargs.get('momentum', 0.9),
            **kwargs
        )
    else:
        raise ValueError(f"Unsupported optimizer type: {optimizer_type}")


def create_scheduler(
    optimizer: torch.optim.Optimizer,
    scheduler_type: str = 'plateau',
    **kwargs
) -> Optional[Any]:
    """
    Create learning rate scheduler.
    
    Args:
        optimizer: Optimizer to schedule
        scheduler_type: Type of scheduler ('plateau', 'step', 'cosine')
        **kwargs: Scheduler-specific parameters
        
    Returns:
        Configured scheduler or None if disabled
    """
    if not getattr(config, 'USE_LR_SCHEDULER', False):
        return None
    
    if scheduler_type.lower() == 'plateau':
        # Remove verbose parameter if present (not supported in newer PyTorch versions)
        scheduler_kwargs = {k: v for k, v in kwargs.items() if k != 'verbose'}
        return ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=getattr(config, 'LR_SCHEDULER_PATIENCE', 10),
            **scheduler_kwargs
        )
    elif scheduler_type.lower() == 'step':
        return StepLR(
            optimizer,
            step_size=getattr(config, 'LR_SCHEDULER_STEP_SIZE', 50),
            gamma=getattr(config, 'LR_SCHEDULER_GAMMA', 0.5),
            **kwargs
        )
    else:
        raise ValueError(f"Unsupported scheduler type: {scheduler_type}")


def optimize_threshold_with_f05(
    y_true: np.ndarray,
    y_proba: np.ndarray,
    num_steps: int = 99,
    min_threshold: float = 0.01,
    max_threshold: float = 0.99,
) -> Tuple[float, float]:
    """
    Optimize threshold for F0.5 score with enhanced handling of concentrated predictions.
    
    Args:
        y_true: True binary labels (0 or 1)
        y_proba: Predicted probabilities (0 to 1)
        num_steps: Number of threshold steps to evaluate
        min_threshold: Minimum threshold value
        max_threshold: Maximum threshold value
        
    Returns:
        Tuple of (optimal_threshold, best_f05_score)
    """
    # Convert to numpy arrays and ensure correct dtypes
    y_true = np.asarray(y_true, dtype=np.float32).flatten()
    y_proba = np.asarray(y_proba, dtype=np.float32).flatten()
    
    # Basic validation
    if len(y_true) == 0 or len(y_proba) == 0:
        return 0.5, 0.0
    
    if len(y_true) != len(y_proba):
        return 0.5, 0.0
    
    # Remove NaN/inf values
    valid_mask = np.isfinite(y_true) & np.isfinite(y_proba)
    if not np.any(valid_mask):
        return 0.5, 0.0
    
    y_true = y_true[valid_mask]
    y_proba = y_proba[valid_mask]
    
    # Check if we have both classes
    unique_labels = np.unique(y_true)
    if len(unique_labels) < 2:
        return 0.5, 0.0
    
    # Count positive samples
    num_positive = np.sum(y_true == 1)
    if num_positive == 0:
        return 0.5, 0.0
    
    # Check prediction spread
    prob_min, prob_max = np.min(y_proba), np.max(y_proba)
    prob_spread = prob_max - prob_min
    prob_std = np.std(y_proba)
    
    # Handle concentrated predictions
    if prob_spread < 0.01 or prob_std < 0.01:
        # Use a more focused search around the mean
        prob_mean = np.mean(y_proba)
        search_range = max(0.02, float(prob_spread) * 2)
        search_min = max(min_threshold, float(prob_mean) - search_range/2)
        search_max = min(max_threshold, float(prob_mean) + search_range/2)
        
        # Create fine-grained threshold range
        thresholds = np.linspace(search_min, search_max, num_steps)
        
        # Also test some additional thresholds around key percentiles
        percentiles = [10, 25, 50, 75, 90]
        for p in percentiles:
            thresh = np.percentile(y_proba, p)
            if search_min <= thresh <= search_max:
                thresholds = np.append(thresholds, thresh)
        
        thresholds = np.unique(thresholds)
        thresholds = thresholds[(thresholds >= min_threshold) & (thresholds <= max_threshold)]
    else:
        # Standard threshold range
        thresholds = np.linspace(min_threshold, max_threshold, num_steps)
    
    # Optimize threshold
    best_f05 = 0.0
    best_threshold = 0.5
    valid_thresholds = 0
    
    for threshold in thresholds:
        # Generate predictions
        y_pred = (y_proba >= threshold).astype(int)
        
        # Calculate F0.5 score
        f05_score = calculate_f05_score(y_true, y_pred)
        
        # Track valid thresholds
        if f05_score > 0:
            valid_thresholds += 1
        
        # Update best
        if f05_score > best_f05:
            best_f05 = f05_score
            best_threshold = threshold
    
    # If no valid thresholds found, return default
    if valid_thresholds == 0:
        return 0.5, 0.0
    
    return float(best_threshold), float(best_f05)


def train_enhanced_ensemble(
    model: nn.Module,
    train_loader: torch.utils.data.DataLoader,
    val_loader: torch.utils.data.DataLoader,
    max_epochs: int = 300,
    device: Optional[torch.device] = None,
    learning_rate: float = 1e-4,
    weight_decay: float = 1e-3,
    patience: int = 35,
    min_epochs: int = 50,
    gradient_clip_norm: float = 1.0,
    verbose: bool = True,
    **kwargs  # Kept for API compatibility
) -> Dict[str, Union[float, List[float], Dict]]:
    """
    ENHANCED ensemble training with robust handling of extreme class imbalance.
    
    ENHANCED FEATURES:
    1. Robust handling of validation sets with very few positive samples
    2. Adaptive learning rate and training strategies
    3. Comprehensive error handling and validation
    4. Enhanced early stopping for imbalanced data
    5. Robust loss computation with fallback strategies
    
    Args:
        model: Model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        max_epochs: Maximum number of training epochs
        device: Device to use for training
        learning_rate: Initial learning rate
        weight_decay: Weight decay for regularization
        patience: Early stopping patience
        min_epochs: Minimum epochs before early stopping
        gradient_clip_norm: Gradient clipping norm
        verbose: Whether to print training progress
        **kwargs: Additional arguments
        
    Returns:
        Dictionary with training results and metrics
    """
    if device is None:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model.to(device)
    
    # ENHANCED: Robust optimizer creation
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=learning_rate,
        weight_decay=weight_decay,
        eps=1e-8  # Stability for extreme cases
    )
    
    # ENHANCED: Analyze training data for imbalance
    train_positive_count = 0
    train_total_count = 0
    
    for batch in train_loader:
        if hasattr(batch, 'y'):
            batch_labels = batch.y.cpu().numpy()
            train_positive_count += np.sum(batch_labels)
            train_total_count += len(batch_labels)
    
    train_positive_ratio = train_positive_count / train_total_count if train_total_count > 0 else 0.0
    
    # ENHANCED: Adaptive class weighting
    if train_positive_ratio < 0.01:  # Less than 1% positive
        pos_weight = torch.tensor(min(100.0, (1.0 - train_positive_ratio) / max(train_positive_ratio, 0.001)), device=device)
        if verbose:
            print(f"   🔧 Extreme imbalance detected ({train_positive_ratio:.3%}), using pos_weight={pos_weight.item():.2f}")
    else:
        pos_weight = torch.tensor((1.0 - train_positive_ratio) / max(train_positive_ratio, 0.001), device=device)
    
    criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    
    # ENHANCED: Adaptive early stopping
    early_stopping = EarlyStopping(
        patience=patience,
        min_epochs=min_epochs,
        verbose=verbose,
        gap_threshold=0.1 if train_positive_ratio < 0.01 else 0.05  # More lenient for extreme imbalance
    )
    
    # ENHANCED: Training history tracking
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_f05': [],
        'val_f05': [],
        'learning_rates': [],
        'epoch_times': []
    }
    
    # ENHANCED: Learning rate scheduler
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='max',  # Maximize F0.5 score
        factor=0.7,
        patience=patience//3,
        min_lr=1e-6
    )
    
    if verbose:
        print(f"🚀 Starting ENHANCED training:")
        print(f"   • Training samples: {train_total_count} ({train_positive_ratio:.3%} positive)")
        print(f"   • Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   • Positive class weight: {pos_weight.item():.2f}")
    
    # ENHANCED: Training loop with comprehensive error handling
    for epoch in range(max_epochs):
        epoch_start_time = time.time()
        
        # ENHANCED: Training phase
        model.train()
        train_loss_sum = 0.0
        train_predictions = []
        train_labels = []
        
        for batch_idx, batch in enumerate(train_loader):
            try:
                batch = batch.to(device)
                optimizer.zero_grad()
                
                # Forward pass with validation
                outputs = model(batch)
                targets = batch.y.float()
                
                # ENHANCED: Validate outputs
                if torch.isnan(outputs).any() or torch.isinf(outputs).any():
                    if verbose:
                        print(f"⚠️ Warning: Invalid outputs at epoch {epoch}, batch {batch_idx}")
                    continue
                
                # SHAPE FIX: Ensure outputs and targets have compatible shapes
                if outputs.dim() == 2 and outputs.size(1) == 1:
                    outputs = outputs.squeeze(1)  # [batch_size, 1] -> [batch_size]
                if targets.dim() == 2:
                    targets = targets.squeeze(1)  # Ensure targets are 1D

                # ENHANCED: Robust loss computation with shape handling
                if hasattr(model, 'compute_enhanced_loss') and callable(model.compute_enhanced_loss):
                    # Use model's enhanced loss computation (handles shapes correctly)
                    loss_result = model.compute_enhanced_loss(outputs, targets, criterion)
                    if isinstance(loss_result, tuple):
                        loss = loss_result[0]  # Total loss
                    else:
                        loss = loss_result
                else:
                    # Fallback: direct criterion call with shape fixes
                    loss = criterion(outputs, targets)
                
                if torch.isnan(loss) or torch.isinf(loss):
                    if verbose:
                        print(f"⚠️ Warning: Invalid loss at epoch {epoch}, batch {batch_idx}")
                    continue
                
                # Backward pass
                loss.backward()
                
                # ENHANCED: Gradient clipping
                if gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), gradient_clip_norm)
                
                optimizer.step()
                
                train_loss_sum += loss.item()
                
                # ENHANCED: Collect predictions for F0.5 calculation
                with torch.no_grad():
                    probs = torch.sigmoid(outputs).cpu().numpy()
                    targets_np = targets.cpu().numpy()

                    # Ensure predictions and targets are 1D
                    if probs.ndim > 1:
                        probs = probs.flatten()
                    if targets_np.ndim > 1:
                        targets_np = targets_np.flatten()

                    train_predictions.extend(probs)
                    train_labels.extend(targets_np)
                    
            except Exception as e:
                if verbose:
                    print(f"⚠️ Warning: Error in training batch {batch_idx}: {e}")
                continue
        
        # ENHANCED: Calculate training metrics
        train_loss = train_loss_sum / len(train_loader) if len(train_loader) > 0 else float('inf')
        
        try:
            # ENHANCED: Robust F0.5 calculation
            if len(train_predictions) > 0 and len(train_labels) > 0:
                train_f05 = _calculate_f05_from_predictions(train_predictions, train_labels)
            else:
                train_f05 = 0.0
        except Exception as e:
            if verbose:
                print(f"⚠️ Warning: Error calculating training F0.5: {e}")
            train_f05 = 0.0
        
        # ENHANCED: Validation phase
        model.eval()
        val_loss_sum = 0.0
        val_predictions = []
        val_labels = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                try:
                    batch = batch.to(device)
                    
                    outputs = model(batch)
                    targets = batch.y.float()

                    # ENHANCED: Validate outputs
                    if torch.isnan(outputs).any() or torch.isinf(outputs).any():
                        continue

                    # SHAPE FIX: Ensure outputs and targets have compatible shapes
                    if outputs.dim() == 2 and outputs.size(1) == 1:
                        outputs = outputs.squeeze(1)  # [batch_size, 1] -> [batch_size]
                    if targets.dim() == 2:
                        targets = targets.squeeze(1)  # Ensure targets are 1D

                    # ENHANCED: Use model's enhanced loss computation if available
                    if hasattr(model, 'compute_enhanced_loss') and callable(model.compute_enhanced_loss):
                        loss_result = model.compute_enhanced_loss(outputs, targets, criterion)
                        if isinstance(loss_result, tuple):
                            loss = loss_result[0]  # Total loss
                        else:
                            loss = loss_result
                    else:
                        loss = criterion(outputs, targets)
                    
                    if torch.isnan(loss) or torch.isinf(loss):
                        continue
                    
                    val_loss_sum += loss.item()
                    
                    # Collect predictions
                    probs = torch.sigmoid(outputs).cpu().numpy()
                    targets_np = targets.cpu().numpy()

                    # Ensure predictions and targets are 1D
                    if probs.ndim > 1:
                        probs = probs.flatten()
                    if targets_np.ndim > 1:
                        targets_np = targets_np.flatten()

                    val_predictions.extend(probs)
                    val_labels.extend(targets_np)
                    
                except Exception as e:
                    if verbose:
                        print(f"⚠️ Warning: Error in validation batch {batch_idx}: {e}")
                    continue
        
        # ENHANCED: Calculate validation metrics
        val_loss = val_loss_sum / len(val_loader) if len(val_loader) > 0 else float('inf')
        
        try:
            if len(val_predictions) > 0 and len(val_labels) > 0:
                val_f05 = _calculate_f05_from_predictions(val_predictions, val_labels)
            else:
                val_f05 = 0.0
        except Exception as e:
            if verbose:
                print(f"⚠️ Warning: Error calculating validation F0.5: {e}")
            val_f05 = 0.0
        
        # ENHANCED: Record history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_f05'].append(train_f05)
        history['val_f05'].append(val_f05)
        history['learning_rates'].append(optimizer.param_groups[0]['lr'])
        history['epoch_times'].append(time.time() - epoch_start_time)
        
        # ENHANCED: Learning rate scheduling
        scheduler.step(val_f05)
        
        # ENHANCED: Progress reporting
        if verbose and (epoch + 1) % 10 == 0:
            gap = abs(train_f05 - val_f05)
            print(f"   Epoch {epoch+1:3d}: Train F0.5={train_f05:.4f}, Val F0.5={val_f05:.4f}, Gap={gap:.4f}")
        
        # ENHANCED: Early stopping check
        if early_stopping(epoch, val_loss, model, train_loss, val_f05, train_f05):
            if verbose:
                print(f"   Training stopped at epoch {epoch+1}")
            break
    
    # ENHANCED: Final results compilation
    results = {
        'best_val_f05': early_stopping.best_val_f05,
        'best_train_f05': history['train_f05'][early_stopping.stopped_epoch] if early_stopping.stopped_epoch > 0 else history['train_f05'][-1],
        'final_val_f05': history['val_f05'][-1],
        'final_train_f05': history['train_f05'][-1],
        'best_val_loss': early_stopping.best_val_loss,
        'final_gap': abs(history['train_f05'][-1] - history['val_f05'][-1]),
        'training_history': history,
        'early_stopping_summary': early_stopping.get_summary(),
        'total_epochs': len(history['train_loss']),
        'model_state': model.state_dict()
    }
    
    # ENHANCED: Performance validation
    if results['best_val_f05'] < 0.001 and train_positive_ratio < 0.01:
        if verbose:
            print(f"⚠️ Warning: Very low F0.5 score ({results['best_val_f05']:.4f}) with extreme imbalance")
            print(f"   This may be normal for highly imbalanced datasets")
    
    return results


def _calculate_f05_from_predictions(predictions: List[float], labels: List[int]) -> float:
    """
    ENHANCED helper function to calculate F0.5 from predictions with robust handling.
    """
    try:
        from . import evaluation
        
        # Find optimal threshold
        threshold, _ = evaluation.optimize_threshold_for_f05(labels, predictions)
        
        # Calculate F0.5 with optimal threshold
        binary_predictions = [1 if p >= threshold else 0 for p in predictions]
        f05 = evaluation.calculate_f05_score(np.array(labels), np.array(binary_predictions))
        
        return f05
        
    except Exception as e:
        print(f"⚠️ Warning: Error in F0.5 calculation: {e}")
        return 0.0


def calculate_composite_f05_score(
    y_true: np.ndarray,
    y_pred: np.ndarray,
    train_f05: float,
    val_f05: float,
    gap_penalty_multiplier: float = 2.5,
) -> float:
    """
    Calculate composite F0.5 score with gap penalty.
    
    Args:
        y_true: True binary labels
        y_pred: Predicted binary labels
        train_f05: Training F0.5 score
        val_f05: Validation F0.5 score
        gap_penalty_multiplier: Penalty multiplier for train-val gap
        
    Returns:
        Composite F0.5 score with gap penalty
    """
    base_f05 = calculate_f05_score(y_true, y_pred)
    gap = max(0, train_f05 - val_f05)  # Only penalize positive gaps
    gap_penalty = gap * gap_penalty_multiplier
    
    return max(0, base_f05 - gap_penalty)


def train_model(
    model: nn.Module,
    train_dataset: List,
    val_dataset: List,
    criterion: nn.Module,
    optimizer: torch.optim.Optimizer,
    device: Optional[torch.device],
    config_obj: Any,
    patience: int = 35,
    num_epochs: int = 300,
    use_weighted_sampler: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Train a model with comprehensive validation and early stopping.
    
    This function provides a complete training loop with validation monitoring,
    early stopping, threshold optimization, and performance tracking.
    
    Args:
        model: PyTorch model to train
        train_dataset: Training dataset (list of PyG Data objects)
        val_dataset: Validation dataset (list of PyG Data objects)
        criterion: Loss function
        optimizer: Optimizer
        device: Device for training
        config_obj: Configuration object
        patience: Early stopping patience
        num_epochs: Maximum number of epochs
        use_weighted_sampler: Whether to use weighted sampling for imbalanced data
        **kwargs: Additional training parameters
        
    Returns:
        Dictionary with training results and metrics
    """
    # Validate inputs
    if not train_dataset:
        raise ValueError("Training dataset is empty")
    
    # Validate device
    device = getattr(config_obj, 'DEVICE', None) if device is None else device
    if device is None:
        device = torch.device('cpu')
    elif not isinstance(device, torch.device):
        device = torch.device(device)
    
    # Create data loaders using PyTorch Geometric DataLoader
    from torch_geometric.loader import DataLoader as GeometricDataLoader
    batch_size = getattr(config_obj, 'BATCH_SIZE', 32)
    
    if use_weighted_sampler and train_dataset:
        # Calculate class weights for weighted sampling
        train_labels = [safe_item(data.y) for data in train_dataset]
        class_weights = data_utils.calculate_class_weights(np.array(train_labels))
        sample_weights = data_utils.get_sampler_weights(np.array(train_labels), class_weights)
        sampler = torch.utils.data.WeightedRandomSampler(sample_weights.tolist(), len(sample_weights))
        train_loader = GeometricDataLoader(train_dataset, batch_size=batch_size, sampler=sampler)  # type: ignore[arg-type]
    else:
        train_loader = GeometricDataLoader(train_dataset, batch_size=batch_size, shuffle=True)  # type: ignore[arg-type]
    
    val_loader = GeometricDataLoader(val_dataset, batch_size=batch_size, shuffle=False) if val_dataset else None  # type: ignore[arg-type]
    
    # Enhanced training with early stopping
    if val_loader:
        history = train_enhanced_ensemble(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            max_epochs=num_epochs,
            device=device,
            learning_rate=optimizer.param_groups[0]['lr'],
            weight_decay=optimizer.defaults.get('weight_decay', 0),
            patience=patience,
            min_epochs=getattr(config_obj, 'MIN_EPOCHS_BEFORE_STOPPING', 50),
            gradient_clip_norm=getattr(config_obj, 'GRADIENT_CLIP_NORM', 1.0),
            verbose=getattr(config_obj, 'VERBOSE_TRAINING', True),
            **kwargs
        )
    else:
        # Training without validation (for final model training)
        warnings.warn("Training without validation dataset - no early stopping will be applied")
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_f05': [],
            'val_f05': [],
            'epochs_completed': 0,
            'best_threshold': 0.5,
            'training_predictions': {}
        }
        
        # Simple training loop without validation
        model.train()
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            train_predictions = []
            train_targets = []
            
            for batch in train_loader:
                batch = batch.to(device)
                optimizer.zero_grad()
                
                outputs = model(batch)
                targets = batch.y.float().unsqueeze(1)
                loss = criterion(outputs, targets)
                
                loss.backward()
                
                # Gradient clipping
                gradient_clip_norm = getattr(config_obj, 'GRADIENT_CLIP_NORM', 1.0)
                if gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), gradient_clip_norm)
                
                optimizer.step()
                
                epoch_loss += safe_item(loss)
                train_predictions.extend(torch.sigmoid(outputs).detach().cpu().numpy())
                train_targets.extend(targets.detach().cpu().numpy())
            
            epoch_loss /= len(train_loader)
            
            # Calculate training F0.5
            train_threshold, train_f05 = optimize_threshold_with_f05(
                np.array(train_targets).flatten(),
                np.array(train_predictions).flatten()
            )
            
            history['train_loss'].append(epoch_loss)
            history['train_f05'].append(train_f05)
            history['epochs_completed'] = epoch + 1  # Ensure 'epoch' is always defined here
            history['best_threshold'] = train_threshold
            
            # Store training predictions for threshold optimization
            if epoch == 0:
                history['training_predictions'] = {
                    'predictions': train_predictions.copy(),
                    'targets': train_targets.copy()
                }
            
            if getattr(config_obj, 'VERBOSE_TRAINING', True) and (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}: Loss={epoch_loss:.6f}, F0.5={train_f05:.4f}")
        
        # CRITICAL FIX: Save model state even without validation
        history['model_state'] = model.state_dict().copy()
    
    # Extract key metrics from history
    # SAFE ACCESS: Handle missing val_f05 key
    best_val_f05 = 0.0
    if 'val_f05' in history and isinstance(history['val_f05'], list) and history['val_f05']:
        best_val_f05 = max(history['val_f05'])
    elif 'train_f05' in history and isinstance(history['train_f05'], list) and history['train_f05']:
        # Fallback to train_f05 if val_f05 is not available
        best_val_f05 = max(history['train_f05'])
    # SAFE ACCESS: Handle missing train_f05 key
    best_train_f05 = 0.0
    if 'train_f05' in history and isinstance(history['train_f05'], list) and history['train_f05']:
        best_train_f05 = max(history['train_f05'])
    # SAFE ACCESS: Handle missing val_loss key
    final_val_loss = 0.0
    if 'val_loss' in history and isinstance(history['val_loss'], list) and history['val_loss']:
        final_val_loss = history['val_loss'][-1]
    # SAFE ACCESS: Handle missing train_loss key
    final_train_loss = 0.0
    if 'train_loss' in history and isinstance(history['train_loss'], list) and history['train_loss']:
        final_train_loss = history['train_loss'][-1]
    
    # CRITICAL FIX: Ensure all metrics are valid floats
    if best_val_f05 is None or np.isnan(best_val_f05) or np.isinf(best_val_f05):
        best_val_f05 = 0.0
    if best_train_f05 is None or np.isnan(best_train_f05) or np.isinf(best_train_f05):
        best_train_f05 = 0.0
    if final_val_loss is None or np.isnan(final_val_loss) or np.isinf(final_val_loss):
        final_val_loss = 0.0
    if final_train_loss is None or np.isnan(final_train_loss) or np.isinf(final_train_loss):
        final_train_loss = 0.0
    
    # Calculate performance metrics
    final_gap = abs(best_train_f05 - best_val_f05) if 'val_f05' in history and isinstance(history['val_f05'], list) and history['val_f05'] else 0.0
    best_epoch = (np.argmax(history['val_f05']) + 1) if 'val_f05' in history and isinstance(history['val_f05'], list) and history['val_f05'] else history.get('epochs_completed', 1)
    
    # CRITICAL FIX: Ensure best_epoch is valid
    if best_epoch is None or not isinstance(best_epoch, (int, float)) or best_epoch <= 0:
        best_epoch = history['epochs_completed']
    
    # Validation loss target achievement
    target_val_loss = getattr(config_obj, 'TARGET_VAL_LOSS', 0.15)
    val_loss_target_achieved = final_val_loss <= target_val_loss if 'val_loss' in history and isinstance(history['val_loss'], list) and history['val_loss'] else False
    
    # Comprehensive results
    results = {
        'history': history,
        'best_val_f05': float(best_val_f05),  # CRITICAL FIX: Ensure float type
        'best_train_f05': float(best_train_f05),  # CRITICAL FIX: Ensure float type
        'final_val_loss': float(final_val_loss),
        'final_train_loss': float(final_train_loss),
        'final_gap': float(final_gap),
        'best_epoch': int(best_epoch) if isinstance(best_epoch, (int, float)) else 1,
        'best_threshold': history.get('best_threshold', 0.5),
        'epochs_completed': history.get('epochs_completed', 1),
        'val_loss_target_achieved': val_loss_target_achieved,
        'final_train_f05': float(best_train_f05),  # For backward compatibility
        'model_state': history.get('model_state', model.state_dict().copy()),  # CRITICAL FIX: Include model state for test evaluation
        'convergence_stats': {
            'epochs_below_target_val_loss': sum(1 for loss in history['val_loss'] if loss <= target_val_loss) if 'val_loss' in history and isinstance(history['val_loss'], list) and history['val_loss'] else 0,
            'val_loss_below_target_ratio': sum(1 for loss in history['val_loss'] if loss <= target_val_loss) / len(history['val_loss']) if 'val_loss' in history and isinstance(history['val_loss'], list) and history['val_loss'] else 0.0
        }
    }
    
    # Add early stopping summary if available
    if 'early_stopping_summary' in history:
        results['early_stopping_summary'] = history['early_stopping_summary']
    
    return results