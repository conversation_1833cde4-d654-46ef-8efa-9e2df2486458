#!/usr/bin/env python3
"""
Test Enhanced TSAP Implementation

Verify that all architectural improvements are working correctly.
"""

import sys
import os
import torch
from torch_geometric.data import Data, Batch

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gnn_lstm import config
from gnn_lstm.models import TSAPGnnLSTM, TemporalSpectralAttentionPooling

def test_enhanced_tsap_initialization():
    """Test that enhanced TSAP initializes correctly with all features."""
    print("🧪 Testing Enhanced TSAP Initialization...")
    
    try:
        # Test TSAP module
        tsap = TemporalSpectralAttentionPooling(
            in_channels=9,
            num_clusters=config.TSAP_NUM_CLUSTERS,
            adjacency_threshold=config.TSAP_ADJACENCY_THRESHOLD,
            lstm_hidden_dim=config.TSAP_CONFIG['lstm_hidden_dim'],
            use_skip_connections=config.TSAP_USE_SKIP_CONNECTIONS,
            use_attention_pooling=config.TSAP_USE_ATTENTION_POOLING,
            use_progressive_pooling=config.TSAP_USE_PROGRESSIVE_POOLING,
            initial_clusters=config.TSAP_INITIAL_CLUSTERS,
        )
        
        print(f"✅ TSAP module initialized successfully")
        print(f"   • Clusters: {tsap.num_clusters}")
        print(f"   • Skip connections: {tsap.use_skip_connections}")
        print(f"   • Attention pooling: {tsap.use_attention_pooling}")
        print(f"   • Progressive pooling: {tsap.use_progressive_pooling}")
        
        # Test full model
        model = TSAPGnnLSTM(
            node_feature_dim=9,
            **config.TSAP_CONFIG
        )
        
        print(f"✅ TSAPGnnLSTM model initialized successfully")
        print(f"   • Total parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False

def test_enhanced_tsap_forward_pass():
    """Test forward pass with enhanced features."""
    print(f"\n🧪 Testing Enhanced TSAP Forward Pass...")
    
    try:
        # Create test data
        batch_size = 2
        num_nodes_per_graph = 118
        node_feature_dim = 9
        
        # Create sample graphs
        graphs = []
        for _ in range(batch_size):
            # Random node features
            x = torch.randn(num_nodes_per_graph, node_feature_dim)

            # Random edges (sparse connectivity)
            num_edges = 50
            edge_index = torch.randint(0, num_nodes_per_graph, (2, num_edges))

            # Create graph
            graph = Data(x=x, edge_index=edge_index)
            graphs.append(graph)
        
        # Create batch
        batch = Batch.from_data_list(graphs)
        
        # Initialize model
        model = TSAPGnnLSTM(
            node_feature_dim=node_feature_dim,
            **config.TSAP_CONFIG
        )
        
        # Forward pass
        model.eval()
        with torch.no_grad():
            output = model(batch)
        
        print(f"✅ Forward pass successful")
        print(f"   • Input shape: {batch.x.shape}")  # type: ignore
        print(f"   • Output shape: {output.shape}")
        print(f"   • Output range: [{output.min():.4f}, {output.max():.4f}]")

        # Test TSAP module directly
        tsap_output = model.tsap(batch.x, batch.edge_index, batch.batch)  # type: ignore
        pooled_features, _, auxiliary_features = tsap_output
        
        print(f"✅ TSAP module working correctly")
        print(f"   • Pooled features shape: {pooled_features.shape}")
        print(f"   • Auxiliary features shape: {auxiliary_features.shape}")
        print(f"   • Expected clusters per graph: {config.TSAP_NUM_CLUSTERS}")
        print(f"   • Actual clusters: {pooled_features.shape[0] // batch_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_skip_connections():
    """Test that skip connections are working properly."""
    print(f"\n🧪 Testing Skip Connections...")
    
    try:
        # Create TSAP with skip connections
        tsap_with_skip = TemporalSpectralAttentionPooling(
            in_channels=9,
            num_clusters=config.TSAP_NUM_CLUSTERS,
            adjacency_threshold=config.TSAP_ADJACENCY_THRESHOLD,
            lstm_hidden_dim=config.TSAP_CONFIG['lstm_hidden_dim'],
            use_skip_connections=True,
            use_attention_pooling=config.TSAP_USE_ATTENTION_POOLING,
        )
        
        # Create TSAP without skip connections
        tsap_without_skip = TemporalSpectralAttentionPooling(
            in_channels=9,
            num_clusters=config.TSAP_NUM_CLUSTERS,
            adjacency_threshold=config.TSAP_ADJACENCY_THRESHOLD,
            lstm_hidden_dim=config.TSAP_CONFIG['lstm_hidden_dim'],
            use_skip_connections=False,
            use_attention_pooling=config.TSAP_USE_ATTENTION_POOLING,
        )
        
        # Test data
        x = torch.randn(118, 9)
        edge_index = torch.randint(0, 118, (2, 50))
        batch = torch.zeros(118, dtype=torch.long)
        
        # Forward pass
        with torch.no_grad():
            output_with_skip = tsap_with_skip(x, edge_index, batch)
            output_without_skip = tsap_without_skip(x, edge_index, batch)
        
        # Compare outputs
        diff = torch.norm(output_with_skip[0] - output_without_skip[0])
        
        print(f"✅ Skip connections test successful")
        print(f"   • Output difference: {diff:.4f}")
        print(f"   • Skip connections {'working' if diff > 0.01 else 'may not be active'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Skip connections test failed: {e}")
        return False

def test_attention_pooling():
    """Test attention-based pooling vs spectral clustering."""
    print(f"\n🧪 Testing Attention Pooling...")
    
    try:
        # Test with attention pooling
        tsap_attention = TemporalSpectralAttentionPooling(
            in_channels=9,
            num_clusters=config.TSAP_NUM_CLUSTERS,
            adjacency_threshold=config.TSAP_ADJACENCY_THRESHOLD,
            lstm_hidden_dim=config.TSAP_CONFIG['lstm_hidden_dim'],
            use_attention_pooling=True,
        )
        
        # Test without attention pooling (spectral clustering)
        tsap_spectral = TemporalSpectralAttentionPooling(
            in_channels=9,
            num_clusters=config.TSAP_NUM_CLUSTERS,
            adjacency_threshold=config.TSAP_ADJACENCY_THRESHOLD,
            lstm_hidden_dim=config.TSAP_CONFIG['lstm_hidden_dim'],
            use_attention_pooling=False,
        )
        
        # Test data
        x = torch.randn(118, 9)
        edge_index = torch.randint(0, 118, (2, 50))
        batch = torch.zeros(118, dtype=torch.long)
        
        # Forward pass
        with torch.no_grad():
            output_attention = tsap_attention(x, edge_index, batch)
            output_spectral = tsap_spectral(x, edge_index, batch)
        
        # Compare outputs
        diff = torch.norm(output_attention[0] - output_spectral[0])
        
        print(f"✅ Attention pooling test successful")
        print(f"   • Output difference: {diff:.4f}")
        print(f"   • Attention pooling {'working' if diff > 0.01 else 'may be similar to spectral'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Attention pooling test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀" + "=" * 60)
    print("🧪 ENHANCED TSAP IMPLEMENTATION TESTING")
    print("🚀" + "=" * 60)
    
    tests = [
        ("Initialization", test_enhanced_tsap_initialization),
        ("Forward Pass", test_enhanced_tsap_forward_pass),
        ("Skip Connections", test_skip_connections),
        ("Attention Pooling", test_attention_pooling),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name.upper()} TEST")
        print(f"{'='*60}")
        
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📋 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {test_name:<20} {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"✅ All tests passed! Enhanced TSAP is ready for training.")
    else:
        print(f"⚠️ Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    main()
