#!/usr/bin/env python3
"""
TSAP Performance Analysis and Improvement Monitoring

This script provides comprehensive analysis of TSAP vs Plain-GNN performance
and monitors the effectiveness of architectural improvements.
"""

import sys
import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gnn_lstm import config
from gnn_lstm.models import TSAPGnnLSTM, PlainGNN

def analyze_tsap_architecture():
    """Analyze TSAP architecture for potential bottlenecks."""
    print("🔍" + "=" * 80)
    print("🎯 TSAP ARCHITECTURE ANALYSIS")
    print("🔍" + "=" * 80)
    
    # Current configuration analysis
    print(f"\n📊 CURRENT TSAP CONFIGURATION:")
    print(f"   • Clusters: {config.TSAP_NUM_CLUSTERS} (compression: {118/config.TSAP_NUM_CLUSTERS:.1f}:1)")
    print(f"   • Adjacency threshold: {config.TSAP_ADJACENCY_THRESHOLD}")
    print(f"   • Spectral components: {config.TSAP_NUM_SPECTRAL_COMPONENTS}")
    print(f"   • Skip connections: {config.TSAP_USE_SKIP_CONNECTIONS}")
    print(f"   • Attention pooling: {config.TSAP_USE_ATTENTION_POOLING}")
    print(f"   • Auxiliary loss: {config.TSAP_USE_AUXILIARY_LOSS}")
    print(f"   • Progressive pooling: {config.TSAP_USE_PROGRESSIVE_POOLING}")
    
    # Architecture capacity analysis
    tsap_config = config.TSAP_CONFIG
    plain_config = config.PLAIN_GNN_CONFIG
    
    print(f"\n⚖️ ARCHITECTURE COMPARISON:")
    print(f"   {'Parameter':<20} {'TSAP':<15} {'Plain-GNN':<15} {'Ratio':<10}")
    print(f"   {'-'*60}")
    
    comparisons = [
        ('LSTM Hidden Dim', tsap_config['lstm_hidden_dim'], 'N/A', 'N/A'),
        ('GNN Hidden Dim', tsap_config['gnn_hidden_dim'], plain_config['gnn_hidden_dim'], 
         f"{tsap_config['gnn_hidden_dim']/plain_config['gnn_hidden_dim']:.2f}x"),
        ('GNN Layers', tsap_config['num_gnn_layers'], plain_config['num_gnn_layers'],
         f"{tsap_config['num_gnn_layers']/plain_config['num_gnn_layers']:.2f}x"),
        ('Attention Heads', tsap_config['num_heads'], plain_config['num_heads'],
         f"{tsap_config['num_heads']/plain_config['num_heads']:.2f}x"),
        ('Dropout Rate', f"{tsap_config['dropout_rate']:.3f}", f"{plain_config['dropout_rate']:.3f}",
         f"{tsap_config['dropout_rate']/plain_config['dropout_rate']:.2f}x"),
    ]
    
    for param, tsap_val, plain_val, ratio in comparisons:
        print(f"   {param:<20} {str(tsap_val):<15} {str(plain_val):<15} {ratio:<10}")
    
    # Information flow analysis
    print(f"\n🔄 INFORMATION FLOW ANALYSIS:")
    input_nodes = 118
    tsap_clusters = config.TSAP_NUM_CLUSTERS
    compression_ratio = input_nodes / tsap_clusters
    
    print(f"   • Input nodes: {input_nodes}")
    print(f"   • TSAP clusters: {tsap_clusters}")
    print(f"   • Compression ratio: {compression_ratio:.2f}:1")
    
    if compression_ratio > 2.5:
        print(f"   ⚠️ HIGH COMPRESSION: May cause information loss")
    elif compression_ratio < 1.5:
        print(f"   ⚠️ LOW COMPRESSION: May not provide sufficient abstraction")
    else:
        print(f"   ✅ BALANCED COMPRESSION: Good information preservation")
    
    return {
        'compression_ratio': compression_ratio,
        'tsap_config': tsap_config,
        'plain_config': plain_config
    }

def estimate_model_complexity():
    """Estimate computational complexity of TSAP vs Plain-GNN."""
    print(f"\n🧮 COMPUTATIONAL COMPLEXITY ANALYSIS:")
    
    # Rough parameter count estimation
    input_dim = 9  # Node feature dimension
    tsap_config = config.TSAP_CONFIG
    plain_config = config.PLAIN_GNN_CONFIG
    
    # TSAP parameter estimation
    tsap_params = 0
    # LSTM parameters
    lstm_hidden = tsap_config['lstm_hidden_dim']
    tsap_params += 4 * (input_dim * lstm_hidden + lstm_hidden * lstm_hidden + lstm_hidden)
    
    # GNN parameters (simplified)
    gnn_hidden = tsap_config['gnn_hidden_dim']
    num_layers = tsap_config['num_gnn_layers']
    num_heads = tsap_config['num_heads']
    tsap_params += num_layers * num_heads * (lstm_hidden * gnn_hidden + gnn_hidden * gnn_hidden)
    
    # Skip connection parameters
    if config.TSAP_USE_SKIP_CONNECTIONS:
        tsap_params += input_dim * lstm_hidden + lstm_hidden * 2 * lstm_hidden
    
    # Plain-GNN parameter estimation
    plain_params = 0
    plain_gnn_hidden = plain_config['gnn_hidden_dim']
    plain_layers = plain_config['num_gnn_layers']
    plain_heads = plain_config['num_heads']
    plain_params += plain_layers * plain_heads * (input_dim * plain_gnn_hidden + plain_gnn_hidden * plain_gnn_hidden)
    
    print(f"   • TSAP estimated parameters: {tsap_params:,}")
    print(f"   • Plain-GNN estimated parameters: {plain_params:,}")
    print(f"   • Parameter ratio: {tsap_params/plain_params:.2f}x")
    
    if tsap_params > plain_params * 2:
        print(f"   ⚠️ TSAP significantly more complex - may need regularization")
    elif tsap_params < plain_params * 0.8:
        print(f"   ⚠️ TSAP less complex - may need more capacity")
    else:
        print(f"   ✅ Balanced complexity between models")
    
    return {
        'tsap_params': tsap_params,
        'plain_params': plain_params,
        'complexity_ratio': tsap_params / plain_params
    }

def analyze_performance_gap():
    """Analyze the current performance gap and identify issues."""
    print(f"\n📉 PERFORMANCE GAP ANALYSIS:")
    
    # Current performance (from comprehensive_results_report.py)
    tsap_f05 = 0.5769
    plain_f05 = 0.8333
    gap = plain_f05 - tsap_f05
    
    print(f"   • TSAP F0.5: {tsap_f05:.4f}")
    print(f"   • Plain-GNN F0.5: {plain_f05:.4f}")
    print(f"   • Performance gap: {gap:.4f} ({gap/plain_f05*100:.1f}%)")
    
    # Target analysis
    target_min = 0.72
    target_max = 0.78
    
    print(f"\n🎯 TARGET ACHIEVEMENT ANALYSIS:")
    print(f"   • Target range: {target_min:.2f} - {target_max:.2f}")
    print(f"   • TSAP gap to target: {target_min - tsap_f05:.4f}")
    print(f"   • Plain-GNN vs target: {'✅ Above' if plain_f05 > target_max else '✅ Within' if plain_f05 >= target_min else '❌ Below'}")
    
    # Improvement needed
    improvement_needed = target_min - tsap_f05
    print(f"   • TSAP improvement needed: +{improvement_needed:.4f} F0.5 points")
    
    return {
        'current_gap': gap,
        'improvement_needed': improvement_needed,
        'tsap_f05': tsap_f05,
        'plain_f05': plain_f05
    }

def generate_improvement_recommendations():
    """Generate specific recommendations for TSAP improvement."""
    print(f"\n💡 IMPROVEMENT RECOMMENDATIONS:")
    
    arch_analysis = analyze_tsap_architecture()
    complexity_analysis = estimate_model_complexity()
    performance_analysis = analyze_performance_gap()
    
    recommendations = []
    
    # Compression ratio recommendations
    if arch_analysis['compression_ratio'] > 2.5:
        recommendations.append({
            'priority': 'HIGH',
            'category': 'Architecture',
            'issue': 'Excessive compression causing information loss',
            'recommendation': f'Increase clusters from {config.TSAP_NUM_CLUSTERS} to 60-70',
            'expected_impact': '+0.05-0.10 F0.5'
        })
    
    # Skip connection recommendations
    if not config.TSAP_USE_SKIP_CONNECTIONS:
        recommendations.append({
            'priority': 'HIGH',
            'category': 'Architecture',
            'issue': 'No information bypass mechanism',
            'recommendation': 'Enable skip connections with proper dimension handling',
            'expected_impact': '+0.03-0.08 F0.5'
        })
    
    # Advanced features recommendations
    if not config.TSAP_USE_ATTENTION_POOLING:
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'Architecture',
            'issue': 'Fixed clustering not adapted to financial data',
            'recommendation': 'Enable attention-based learnable clustering',
            'expected_impact': '+0.02-0.05 F0.5'
        })
    
    # Regularization recommendations
    tsap_dropout = arch_analysis['tsap_config']['dropout_rate']
    plain_dropout = arch_analysis['plain_config']['dropout_rate']
    if tsap_dropout > plain_dropout * 1.2:
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'Training',
            'issue': 'Over-regularization compared to successful Plain-GNN',
            'recommendation': f'Reduce dropout from {tsap_dropout} to {plain_dropout}',
            'expected_impact': '+0.01-0.03 F0.5'
        })
    
    # Capacity recommendations
    if complexity_analysis['complexity_ratio'] < 0.8:
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'Architecture',
            'issue': 'Insufficient model capacity',
            'recommendation': 'Increase LSTM and GNN hidden dimensions',
            'expected_impact': '+0.02-0.06 F0.5'
        })
    
    # Print recommendations
    for i, rec in enumerate(recommendations, 1):
        print(f"\n   {i}. [{rec['priority']}] {rec['category']}: {rec['issue']}")
        print(f"      💡 {rec['recommendation']}")
        print(f"      📈 Expected impact: {rec['expected_impact']}")
    
    return recommendations

def main():
    """Main analysis function."""
    print("🚀" + "=" * 89)
    print("🎯 TSAP PERFORMANCE ANALYSIS & IMPROVEMENT RECOMMENDATIONS")
    print("🚀" + "=" * 89)
    
    # Run comprehensive analysis
    arch_analysis = analyze_tsap_architecture()
    complexity_analysis = estimate_model_complexity()
    performance_analysis = analyze_performance_gap()
    recommendations = generate_improvement_recommendations()
    
    # Summary
    print(f"\n📋 ANALYSIS SUMMARY:")
    print(f"   • Current performance gap: {performance_analysis['current_gap']:.4f} F0.5 points")
    print(f"   • Improvement needed: +{performance_analysis['improvement_needed']:.4f} F0.5 points")
    print(f"   • Compression ratio: {arch_analysis['compression_ratio']:.2f}:1")
    print(f"   • Complexity ratio: {complexity_analysis['complexity_ratio']:.2f}x")
    print(f"   • High priority recommendations: {len([r for r in recommendations if r['priority'] == 'HIGH'])}")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Apply architectural improvements (clusters, skip connections)")
    print(f"   2. Enable advanced TSAP features (attention pooling, auxiliary loss)")
    print(f"   3. Optimize hyperparameters (dropout, learning rate)")
    print(f"   4. Run enhanced training with monitoring")
    print(f"   5. Validate performance improvements")

if __name__ == "__main__":
    main()
