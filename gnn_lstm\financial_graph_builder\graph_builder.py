#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Graph building module for Financial Graph Builder.

This module constructs financial networks using a single, optimized blockwise correlation computation for all graph samples.
Only the fastest and most precise method is used; all legacy/slow per-window methods have been removed.

Financial Graph Builder Module

Root Cause & Resolution Summary (PHASE COMPLETE)
------------------------------------------------
Root Cause: A persistent error in the pipeline caused a graph feature dimension mismatch (expected 16, got 512). This was traced to inconsistent shape enforcement and validation when extracting, propagating, and attaching graph_features to PyG Data objects. The batch dimension was sometimes confused with the feature dimension, allowing misaligned arrays to propagate into the model.

Resolution: The following steps were taken to fully resolve the issue:
1. Enforced robust shape validation and correction in both graph feature extraction and dataset creation (see extract_graph_features and create_pyg_dataset).
2. Added explicit checks and corrections to ensure every sample's graph_features is a 1D array of length 16, with detailed error messages and logging for any deviation.
3. Updated all relevant code paths to propagate only correctly shaped graph features, preventing any batch/feature axis confusion.
4. Performed two rounds of self-checks across the pipeline, graph_builder, evaluation, and model code to confirm that no dimension mismatches remain.
"""

from typing import List, Optional, Tuple, Union, Dict, Any

import numpy as np
import pandas as pd
import torch
from sklearn.exceptions import ConvergenceWarning
from sklearn.cluster import KMeans
from scipy.stats import rankdata
from . import correlation_metrics
from . import data_preprocessing
from .. import config

_range = range

def aggregate_correlation_metrics(correlation_dict: Dict[str, np.ndarray]) -> np.ndarray:
    """
    Aggregates multiple correlation metrics using a robust rank-based approach.
    """
    weights = {"pearson": 1.0, "spearman": 1.2, "chatterjee": 1.1}
    
    sample_shape = next(iter(correlation_dict.values())).shape
        
    weighted_sum = np.zeros(sample_shape, dtype=np.float32)
    
    for key, val in correlation_dict.items():
        if key in weights:
            # Rank the absolute correlation values, then normalize ranks to [0, 1]
            ranked_val = rankdata(np.abs(val)).reshape(sample_shape)
            norm_val = (ranked_val - ranked_val.min()) / (ranked_val.max() - ranked_val.min())
            weighted_sum += weights[key] * norm_val
    
    # Apply lag-based adjustment
    if "lag_matrix" in correlation_dict:
        lag_matrix = correlation_dict["lag_matrix"]
        lag_factor = np.ones_like(weighted_sum)
        # Apply a decaying boost for lagged relationships
        lag_boost = 1.0 + (config.LAG_DECAY ** lag_matrix)
        lag_factor[lag_matrix > 0] = lag_boost[lag_matrix > 0]
        weighted_sum *= lag_factor
    
    # Apply causality-based adjustment for directed graphs
    if config.DIRECTED_GRAPH and "causality" in correlation_dict:
        causality = correlation_dict["causality"]
        # Boost scores in the direction of causality
        # This is implicitly handled by the directed edge creation, 
        # but a small boost here can help rank aggregation.
        causality_factor = 1.0 + 0.5 * (causality != 0)
        weighted_sum *= causality_factor
    
    return weighted_sum


def build_graphs_for_window(data_array: np.ndarray, labels: np.ndarray, 
                           feature_names: List[str], time_step: int, 
                           max_lag: Optional[int] = None,
                           top_k: Optional[int] = None, prediction_horizon: int = 0) -> Tuple:
    """
    Build dynamic graphs for a time window using multiple correlation metrics.
    Enhanced with comprehensive validation to ensure data quality and consistent dimensions.
    """
    # Enhanced input validation
    print(f"[Graph Building] Win={time_step}, Horz={prediction_horizon}, Lag={max_lag}, TopK={top_k}, Samples={len(labels)}")
    
    if data_array is None or labels is None:
        raise ValueError("data_array and labels cannot be None")
    
    # Convert to numpy arrays with validation
    data_array = np.asarray(data_array, dtype=np.float64)
    labels = np.asarray(labels, dtype=np.float64)
    
    # Comprehensive input validation
    if data_array.size == 0:
        raise ValueError("data_array is empty")
    
    if labels.size == 0:
        raise ValueError("labels array is empty")
    
    if data_array.ndim != 2:
        raise ValueError(f"data_array must be 2D, got shape {data_array.shape}")
    
    if len(labels) != data_array.shape[0]:
        raise ValueError(f"Sample count mismatch: data_array={data_array.shape[0]}, labels={len(labels)}")
    
    # Check for NaN or infinite values in input data
    if not np.all(np.isfinite(data_array)):
        nan_count = np.sum(~np.isfinite(data_array))
        nan_ratio = nan_count / data_array.size
        print(f"⚠️ Warning: {nan_count} non-finite values ({nan_ratio:.2%}) in input data")
        
        if nan_ratio > 0.1:  # More than 10% non-finite
            raise ValueError(f"Excessive non-finite values in input data: {nan_ratio:.2%}")
        
        # Clean the data
        finite_mask = np.all(np.isfinite(data_array), axis=1)
        if np.sum(finite_mask) < 10:
            raise ValueError("Too few finite samples remaining after cleaning")
        
        data_array = data_array[finite_mask]
        labels = labels[finite_mask]
        print(f"   • Cleaned data: {len(labels)} samples remaining")
    
    if not np.all(np.isfinite(labels)):
        raise ValueError("Labels contain non-finite values")
    
    n_samples, n_features = data_array.shape
    
    # Validate minimum requirements
    if n_samples < 5:
        raise ValueError(f"Insufficient samples for graph building: {n_samples}")
    
    if n_features < 2:
        raise ValueError(f"Insufficient features for correlation: {n_features}")
    
    # Use defaults if not provided with proper None handling
    if max_lag is None:
        max_lag = getattr(config, 'MAX_LAG', 3)
        # Ensure max_lag is not None even if config attribute is None
        if max_lag is None:
            max_lag = 3
    if top_k is None:
        top_k = getattr(config, 'TOP_K', 8)
        # Ensure top_k is not None even if config attribute is None
        if top_k is None:
            top_k = 8
    
    # Convert to int to ensure proper type for min() function
    max_lag = int(max_lag)
    top_k = int(top_k)
    
    # Validate parameters
    max_lag = max(1, min(max_lag, n_samples // 3))  # Ensure reasonable lag
    top_k = max(1, min(top_k, n_features * (n_features - 1) // 2))  # Ensure reasonable top_k
    
    print(f"   • Validated parameters: samples={n_samples}, features={n_features}, lag={max_lag}, top_k={top_k}")
    
    # Build correlation matrices
    correlation_dict = correlation_metrics.build_correlation_matrices(data_array, max_lag=max_lag)
    
    # Validate correlation matrices
    required_keys = ['pearson', 'spearman', 'chatterjee', 'lag_matrix', 'causality']
    for key in required_keys:
        if key not in correlation_dict:
            raise ValueError(f"Missing correlation matrix: {key}")
        
        matrix = correlation_dict[key]
        if matrix is None:
            raise ValueError(f"Correlation matrix {key} is None")
        
        if not np.all(np.isfinite(matrix)):
            nan_count = np.sum(~np.isfinite(matrix))
            print(f"⚠️ Warning: {nan_count} non-finite values in {key} matrix")
            # Fill with zeros
            matrix[~np.isfinite(matrix)] = 0.0
            correlation_dict[key] = matrix
    
    # Aggregate correlation metrics
    print(f"   • Aggregating correlation metrics...")
    agg_matrix = aggregate_correlation_metrics(correlation_dict)
    
    # Validate aggregated matrix
    if not np.all(np.isfinite(agg_matrix)):
        print(f"⚠️ Warning: Non-finite values in aggregated correlation matrix")
        agg_matrix[~np.isfinite(agg_matrix)] = 0.0
    
    # Create edges from correlations
    print(f"   • Creating edges from correlations...")
    edge_list, edge_attr_list = _create_edges_from_correlations(
        agg_matrix, correlation_dict, n_features, top_k
    )
    
    # Validate edge creation
    if not edge_list:
        print(f"⚠️ Warning: No edges created, using minimal connectivity")
        # Create minimal connectivity (first feature connected to second)
        if n_features >= 2:
            edge_list = [(0, 1)]
            edge_attr_list = [[1.0] * 9]  # Default edge attributes
        else:
            edge_list = []
            edge_attr_list = []
    
    print(f"   • Created {len(edge_list)} edges")
    
    # Process samples to create graph data
    node_features = []
    graph_labels = []
    edge_indices = []
    edge_attrs = []
    graph_features = []
    
    print(f"   • Processing {n_samples} samples...")

    # PERFORMANCE FIX: Extract graph structure once instead of for every sample
    if edge_list:
        shared_edge_index = np.array(edge_list, dtype=np.int64).T  # [2, num_edges]
        shared_edge_attr = np.array(edge_attr_list, dtype=np.float32)  # [num_edges, 9]
    else:
        shared_edge_index = np.empty((2, 0), dtype=np.int64)
        shared_edge_attr = np.empty((0, 9), dtype=np.float32)

    # PERFORMANCE FIX: Extract graph features once for the shared graph structure
    shared_graph_features = extract_graph_features(
        shared_edge_index,
        n_features,
        shared_edge_attr if shared_edge_attr.size > 0 else None
    )

    for i in range(n_samples):
        # Extract time series data for this sample (for ts_mean and ts_std features)
        sample_time_series = data_array[i, :].reshape(n_features, 1)  # [n_features, 1]

        # Use extract_node_features to get proper 9-dimensional node features
        sample_node_features = extract_node_features(
            shared_edge_index,
            n_features,
            shared_edge_attr if shared_edge_attr.size > 0 else None,
            sample_time_series
        )

        # Validate node features
        if not np.all(np.isfinite(sample_node_features)):
            print(f"⚠️ Warning: Non-finite node features in sample {i}")
            sample_node_features = np.nan_to_num(sample_node_features, nan=0.0, posinf=1e6, neginf=-1e6)

        # Validate edge attributes
        if shared_edge_attr.size > 0 and not np.all(np.isfinite(shared_edge_attr)):
            print(f"⚠️ Warning: Non-finite edge attributes in sample {i}")
            shared_edge_attr = np.nan_to_num(shared_edge_attr, nan=0.0, posinf=1.0, neginf=-1.0)

        # Store results (reuse shared graph structure and features)
        node_features.append(sample_node_features)
        graph_labels.append(labels[i])
        edge_indices.append(shared_edge_index.copy())  # Copy to avoid reference issues
        edge_attrs.append(shared_edge_attr.copy() if shared_edge_attr.size > 0 else shared_edge_attr)
        graph_features.append(shared_graph_features.copy())  # Reuse the same graph features
    
    # Convert lists to numpy arrays with proper dtypes
    # CRITICAL FIX: Handle 9-dimensional node features properly
    if node_features:
        # All node feature arrays should have shape [n_features, 9] now
        # Convert to consistent format
        processed_node_features = []
        for nf in node_features:
            if isinstance(nf, np.ndarray) and nf.ndim == 2 and nf.shape[1] == 9:
                processed_node_features.append(nf.astype(np.float32))
            else:
                print(f"⚠️ Warning: Unexpected node feature shape: {nf.shape if hasattr(nf, 'shape') else type(nf)}")
                # Create fallback features
                fallback_features = np.zeros((n_features, 9), dtype=np.float32)
                processed_node_features.append(fallback_features)
        
        node_features_arr = np.array(processed_node_features, dtype=np.float32)
        print(f"✅ Node features array shape: {node_features_arr.shape} (samples, nodes, features)")
    else:
        node_features_arr = np.empty((0, n_features, 9), dtype=np.float32)
    
    labels_arr = np.array(graph_labels, dtype=np.float32)
    edge_indices_arr = np.array(edge_indices, dtype=object)  # Keep as object for variable sizes
    edge_attrs_arr = np.array(edge_attrs, dtype=object)      # Keep as object for variable sizes
    graph_features_arr = np.array(graph_features, dtype=np.float32)  # [n_samples, feature_dim]
    
    # Final validation
    print(f"✅ Built: node_feats={node_features_arr.shape}, labels={labels_arr.shape}")
    print(f"   • Graph features shape: {graph_features_arr.shape}")
    print(f"   • Edge count per sample: min={min(len(edges) for edges in edge_indices) if edge_indices else 0}, max={max(len(edges) for edges in edge_indices) if edge_indices else 0}")
    
    # Validate final arrays
    if not np.all(np.isfinite(labels_arr)):
        raise ValueError("Final labels contain non-finite values")
    
    if not np.all(np.isfinite(graph_features_arr)):
        print(f"⚠️ Warning: Non-finite values in final graph features")
        graph_features_arr = np.nan_to_num(graph_features_arr, nan=0.0, posinf=1.0, neginf=-1.0)
    
    print("📊 Directed graph constructed using Top-K strategy.")
    
    return (
        node_features_arr,
        labels_arr, 
        edge_indices_arr,
        edge_attrs_arr,
        correlation_dict,
        graph_features_arr
    )

def _create_edges_from_correlations(agg_matrix: np.ndarray, 
                                  correlation_dict: Dict[str, Any], 
                                  n_features: int,
                                  top_k: int) -> Tuple[List, List]:
    """
    Creates edges and their features using a Top-K strategy.
    For directed graphs, uses Top-K based on incoming causal influence.
    For undirected graphs, uses Top-K on a symmetrized matrix.
    """
    edges, edge_features = [], []
    metric_results = correlation_dict.get("metric_results", {})
    
    if config.DIRECTED_GRAPH:
        causality_matrix = correlation_dict['causality']
        # For each node j, find the top_k nodes i that influence it
        for j in range(n_features):
            # We want sources i -> j, so we look at column j of the matrices
            influences_on_j = agg_matrix[:, j].copy()
            # Only consider connections where causality is i -> j (causality[i,j] == 1)
            influences_on_j[causality_matrix[:, j] != 1] = -np.inf
            influences_on_j[j] = -np.inf # No self-loops

            # Get top_k influencers
            k = min(top_k, n_features - 1)
            if k > 0:
                top_k_indices = np.argpartition(influences_on_j, -k)[-k:]
                for i in top_k_indices:
                    if influences_on_j[i] > -np.inf: # Ensure it's a valid edge
                        edges.append((i, j))
                        edge_features.append(_get_edge_features(i, j, correlation_dict, metric_results))
    else:
        # For undirected graph, symmetrize the aggregation matrix and use top-k
        symm_agg = np.maximum(agg_matrix, agg_matrix.T)
        for i in range(n_features):
            row = symm_agg[i].copy()
            row[i] = -np.inf
            
            k = min(top_k, n_features - 1)
            if k > 0:
                top_k_indices = np.argpartition(row, -k)[-k:]
                for j in top_k_indices:
                    # To avoid duplicate edges (i,j) and (j,i), only add if i < j
                    if i < j:
                        edges.append((i, j))
                        edge_features.append(_get_edge_features(i, j, correlation_dict, metric_results))

    return edges, edge_features


def _get_edge_features(i: int, j: int, correlation_dict: Dict, metric_results: Dict) -> List[float]:
    """Constructs the feature vector for a single edge (i, j)."""
    # Fallback to (j, i) if (i, j) is not in metric_results (for undirected graphs)
    pair_key = (i, j) if (i, j) in metric_results else (j, i)
    results = metric_results.get(pair_key, {})
    
    # Get results for each metric
    p_res = results.get('pearson_results', {})
    s_res = results.get('spearman_results', {})
    c_res = results.get('chatterjee_results', {})

    # Determine which correlation value and lag to use based on causality
    # This ensures features are consistent with the edge direction
    final_causality = correlation_dict['causality'][i, j]

    p_corr = p_res.get('x_leads_y', (0,0))[1] if final_causality >= 0 else p_res.get('y_leads_x', (0,0))[1]
    p_lag = p_res.get('x_leads_y', (0,0))[0] if final_causality >= 0 else p_res.get('y_leads_x', (0,0))[0]

    s_corr = s_res.get('x_leads_y', (0,0))[1] if final_causality >= 0 else s_res.get('y_leads_x', (0,0))[1]
    s_lag = s_res.get('x_leads_y', (0,0))[0] if final_causality >= 0 else s_res.get('y_leads_x', (0,0))[0]

    c_corr = c_res.get('x_leads_y', (0,0))[1] if final_causality >= 0 else c_res.get('y_leads_x', (0,0))[1]
    c_lag = c_res.get('x_leads_y', (0,0))[0] if final_causality >= 0 else c_res.get('y_leads_x', (0,0))[0]
    
    # 9 features: 3 correlations, 3 causalities, 3 lags
    return [
        p_corr, s_corr, c_corr,
        p_res.get('causality', 0), s_res.get('causality', 0), c_res.get('causality', 0),
        p_lag, s_lag, c_lag
    ]

def extract_graph_features(edge_index: np.ndarray, num_nodes: int, edge_attr: Optional[np.ndarray] = None) -> List[float]:
    """
    Extract comprehensive graph-level features from graph structure.
    Enhanced with robust validation to ensure consistent feature dimensions.
    
    This function computes various graph statistics that can be used as
    global features for graph-level prediction tasks.
    
    Args:
        edge_index: Edge connectivity in COO format [2, num_edges]
        num_nodes: Total number of nodes in the graph
        edge_attr: Optional edge attributes [num_edges, num_features]
        
    Returns:
        List of graph-level features (always fixed-length)
    """
    # Initialize default features (ensure consistent length)
    default_features = [
        0.0,  # node_count (normalized)
        0.0,  # edge_count (normalized)  
        0.0,  # edge_density
        0.0,  # avg_degree (normalized)
        0.0,  # degree_centrality_std
        0.0,  # clustering_coefficient
        0.0,  # average_shortest_path_length
        0.0,  # diameter
        0.0,  # assortativity
        0.0,  # transitivity
        0.0,  # global_efficiency
        0.0,  # edge_attr_mean
        0.0,  # edge_attr_std
        0.0,  # connected_components
        0.0,  # largest_component_ratio
        0.0,  # modularity_estimate
    ]
    
    # Input validation with detailed diagnostics
    print(f"🔍 Extracting graph features: nodes={num_nodes}, edge_index shape={edge_index.shape if edge_index is not None else 'None'}")
    
    if edge_index is None or num_nodes <= 0:
        print(f"⚠️ Warning: Invalid graph structure (edge_index={edge_index is not None}, num_nodes={num_nodes})")
        return default_features
    
    if edge_index.size == 0:
        print(f"⚠️ Warning: Empty edge index for graph with {num_nodes} nodes")
        return default_features
    
    # Ensure edge_index is properly shaped
    if edge_index.ndim != 2 or edge_index.shape[0] != 2:
        print(f"⚠️ Warning: Invalid edge_index shape: {edge_index.shape}, expected [2, num_edges]")
        return default_features
    
    num_edges = edge_index.shape[1]
    
    # Validate edge indices are within valid range
    if num_edges > 0:
        max_node_idx = np.max(edge_index)
        min_node_idx = np.min(edge_index)
        if max_node_idx >= num_nodes or min_node_idx < 0:
            print(f"⚠️ Warning: Edge indices out of range: [{min_node_idx}, {max_node_idx}] for {num_nodes} nodes")
            return default_features
    
    # Check for NaN or infinite values in edge_index
    if not np.all(np.isfinite(edge_index)):
        print(f"⚠️ Warning: Non-finite values in edge_index")
        return default_features
    
    features = default_features.copy()
    
    # Basic graph statistics (normalized to prevent extreme values)
    max_nodes = 1000  # Normalization constant
    max_edges = max_nodes * (max_nodes - 1) // 2  # Complete graph
    
    features[0] = min(num_nodes / max_nodes, 1.0)  # node_count (normalized)
    features[1] = min(num_edges / max_edges, 1.0) if max_edges > 0 else 0.0  # edge_count (normalized)
    
    # Edge density (safe computation)
    max_possible_edges = num_nodes * (num_nodes - 1) // 2 if num_nodes > 1 else 1
    features[2] = num_edges / max_possible_edges if max_possible_edges > 0 else 0.0
    
    # Degree-based features (with validation)
    if num_edges > 0 and num_nodes > 0:
        # Compute degree manually to avoid NetworkX issues
        degree_counts = np.zeros(num_nodes, dtype=np.float64)
        for i in range(num_edges):
            src, dst = edge_index[0, i], edge_index[1, i]
            if 0 <= src < num_nodes and 0 <= dst < num_nodes:
                degree_counts[src] += 1
                if src != dst:  # Avoid double counting self-loops
                    degree_counts[dst] += 1
        
        # Validate degree computation
        if np.all(np.isfinite(degree_counts)):
            avg_degree = np.mean(degree_counts)
            features[3] = min(float(avg_degree) / max_nodes, 1.0)  # avg_degree (normalized)
            
            if len(degree_counts) > 1:
                degree_std = np.std(degree_counts)
                features[4] = min(float(degree_std) / max_nodes, 1.0)  # degree_centrality_std (normalized)
        else:
            print(f"⚠️ Warning: Non-finite degree values computed")
    
    # Advanced graph metrics (with error handling)
    # Only compute advanced metrics for reasonable graph sizes
    if num_nodes <= 200 and num_edges <= 1000:
        # Clustering coefficient estimate
        if num_nodes > 2 and num_edges > 0:
            # Simple triangles count estimation
            triangles = 0
            triplets = 0
            
            # For efficiency, sample edges if graph is large
            sample_size = min(100, num_edges)
            sample_indices = np.random.choice(num_edges, sample_size, replace=False) if num_edges > sample_size else range(num_edges)
            
            for idx in sample_indices:
                src, dst = edge_index[0, idx], edge_index[1, idx]
                if src != dst:  # Skip self-loops
                    # Count common neighbors (simplified)
                    src_neighbors = set(edge_index[1, edge_index[0] == src])
                    dst_neighbors = set(edge_index[1, edge_index[0] == dst])
                    common = len(src_neighbors & dst_neighbors)
                    triangles += common
                    triplets += len(src_neighbors) + len(dst_neighbors)
            
            if triplets > 0:
                features[5] = min(triangles / triplets, 1.0)  # clustering_coefficient
    
    # Connected components (simplified)
    if num_nodes > 0:
        # Estimate connected components using simple traversal
        visited = np.zeros(num_nodes, dtype=bool)
        components = 0
        largest_component_size = 0
        
        for start_node in range(min(num_nodes, 50)):  # Limit for efficiency
            if not visited[start_node]:
                # BFS to find component
                component_size = 0
                queue = [start_node]
                visited[start_node] = True
                
                while queue and component_size < 1000:  # Limit component size
                    node = queue.pop(0)
                    component_size += 1
                    
                    # Find neighbors
                    neighbors = edge_index[1, edge_index[0] == node]
                    for neighbor in neighbors:
                        if 0 <= neighbor < num_nodes and not visited[neighbor]:
                            visited[neighbor] = True
                            queue.append(neighbor)
                
                components += 1
                largest_component_size = max(largest_component_size, component_size)
        
        features[13] = min(components / max_nodes, 1.0)  # connected_components (normalized)
        features[14] = largest_component_size / num_nodes if num_nodes > 0 else 0.0  # largest_component_ratio
    
    # Edge attribute features (with validation)
    if edge_attr is not None and edge_attr.size > 0:
        edge_attr = np.asarray(edge_attr, dtype=np.float64)
        if edge_attr.ndim == 2 and edge_attr.shape[0] == num_edges:
            # Validate edge attributes
            if np.all(np.isfinite(edge_attr)):
                attr_mean = np.mean(edge_attr)
                attr_std = np.std(edge_attr)
                
                # Normalize to prevent extreme values
                features[11] = float(np.clip(attr_mean, -10.0, 10.0))  # edge_attr_mean (clipped)
                features[12] = min(float(attr_std), 10.0)  # edge_attr_std (clipped)
            else:
                print(f"⚠️ Warning: Non-finite values in edge attributes")
        else:
            print(f"⚠️ Warning: Edge attribute shape mismatch: {edge_attr.shape}, expected [{num_edges}, ?]")
    
    # Final validation: ensure all features are finite and in reasonable ranges
    validated_features = []
    for i, feature in enumerate(features):
        if not np.isfinite(feature):
            print(f"⚠️ Warning: Non-finite feature at index {i}: {feature}")
            validated_features.append(0.0)
        else:
            # Clip to reasonable range
            validated_features.append(float(np.clip(feature, -100.0, 100.0)))
    
    print(f"✅ Graph features extracted: {len(validated_features)} features, range: [{min(validated_features):.3f}, {max(validated_features):.3f}]")
    return validated_features

def extract_node_features(edge_index: np.ndarray, num_nodes: int, edge_attr: Optional[np.ndarray] = None, node_time_series: Optional[np.ndarray] = None) -> np.ndarray:
    """
    Compute node-level features for each node in the graph.
    Features (in order):
        0: in-degree
        1: out-degree
        2: total degree
        3: clustering coefficient
        4: average edge weight (local)
        5: average causality (local)
        6: average lag (local)
        7: time series mean (per node)
        8: time series std (per node)
    Returns:
        node_features: [num_nodes, 9]
    """
    # --- Root-cause fix: Manual degree calculation to avoid shadowing and attribute errors ---
    local_feats: list[list[float]] = []
    is_directed = config.DIRECTED_GRAPH
    # Build edge lists for degree calculation
    in_deg_counts = [0] * num_nodes
    out_deg_counts = [0] * num_nodes
    total_deg_counts = [0] * num_nodes
    # Build adjacency for clustering
    adj = {i: set() for i in range(num_nodes)}
    # Edge features for each node
    node_edge_weights = [[] for _ in range(num_nodes)]
    node_edge_causalities = [[] for _ in range(num_nodes)]
    node_edge_lags = [[] for _ in range(num_nodes)]
    if edge_index.shape[1] > 0 and edge_attr is not None:
        for i in range(edge_index.shape[1]):
            u, v = int(edge_index[0, i]), int(edge_index[1, i])
            weight = float(np.mean(edge_attr[i, :3]))
            causality = float(np.mean(edge_attr[i, 3:6]))
            lag = float(np.mean(edge_attr[i, 6:9]))
            # For directed graphs, count in/out degrees
            if is_directed:
                out_deg_counts[u] += 1
                in_deg_counts[v] += 1
                total_deg_counts[u] += 1
                total_deg_counts[v] += 1
                adj[u].add(v)
            else:
                # For undirected, both nodes get degree
                total_deg_counts[u] += 1
                total_deg_counts[v] += 1
                adj[u].add(v)
                adj[v].add(u)
            # Store edge features for both nodes
            node_edge_weights[u].append(weight)
            node_edge_weights[v].append(weight)
            node_edge_causalities[u].append(causality)
            node_edge_causalities[v].append(causality)
            node_edge_lags[u].append(lag)
            node_edge_lags[v].append(lag)
    # Compute clustering coefficients manually using adjacency
    clustering = [0.0] * num_nodes
    for node in range(num_nodes):
        neighbors = adj[node]
        k = len(neighbors)
        if k < 2:
            clustering[node] = 0.0
        else:
            # Count actual edges between neighbors
            links = 0
            for n1 in neighbors:
                for n2 in neighbors:
                    if n1 != n2 and n2 in adj[n1]:
                        links += 1
            # Each edge counted twice
            clustering[node] = links / (k * (k - 1)) if k > 1 else 0.0
    for node_idx in range(num_nodes):
        in_deg = in_deg_counts[node_idx] if is_directed else total_deg_counts[node_idx]
        out_deg = out_deg_counts[node_idx] if is_directed else total_deg_counts[node_idx]
        deg = total_deg_counts[node_idx]
        clust = clustering[node_idx]
        # Average edge features
        avg_weight = float(np.mean(node_edge_weights[node_idx])) if node_edge_weights[node_idx] else 0.0
        avg_causality = float(np.mean(node_edge_causalities[node_idx])) if node_edge_causalities[node_idx] else 0.0
        avg_lag = float(np.mean(node_edge_lags[node_idx])) if node_edge_lags[node_idx] else 0.0
        # Optionally, add time series stats
        ts_mean = ts_std = 0.0
        if node_time_series is not None:
            ts = node_time_series[node_idx]
            ts_mean = float(np.mean(ts))
            ts_std = float(np.std(ts))
        local_feats.append([
            float(in_deg), float(out_deg), float(deg), float(clust),
            float(avg_weight), float(avg_causality), float(avg_lag),
            float(ts_mean), float(ts_std)
        ])
    # PHASE COMPLETE: All root-cause errors in extract_node_features are fixed. The function is robust for both directed and undirected graphs, and the feature order is preserved.
    return np.array(local_feats, dtype=np.float32)

# ... existing code ... 