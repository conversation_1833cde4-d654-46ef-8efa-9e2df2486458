#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GNN-LSTM Models Module

This module contains the model architectures for the enhanced GNN-LSTM financial
crisis prediction system, including:

1. TSAP (Temporal Spectral Attention Pooling) - Advanced graph coarsening
2. TSAPGnnLSTM - Primary model with TSAP pooling
3. PlainGNN - Baseline model without TSAP for comparison

All models include architectural enhancements: dropout, residual connections,
and layer normalization for improved training stability and performance.
"""

from typing import Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.cluster import KMeans
from sklearn.exceptions import ConvergenceWarning
from torch_geometric.nn import (
    GATConv,
    GraphConv,
    TransformerConv,
    global_max_pool,
    global_mean_pool,
)

from . import config


def safe_item(value):
    """
    Safely extract scalar value from either a tensor or already-scalar value.
    
    Args:
        value: Either a PyTorch tensor or a scalar (int, float, etc.)
        
    Returns:
        Scalar value (int, float, etc.)
    """
    if hasattr(value, 'item'):
        return value.item()
    else:
        return value


# --- Main Model Architectures ---


class TemporalSpectralAttentionPooling(nn.Module):
    """
    Enhanced Temporal Spectral Attention Pooling with Information-Preserving Architecture.
    
    TSAP-ENHANCED FEATURES:
    - Skip connections to bypass information bottlenecks
    - Attention-based learnable clustering vs fixed K-means
    - Progressive pooling with adaptive cluster count
    - Multi-objective auxiliary outputs for better training
    
    This enhanced version addresses the core performance issues of the original TSAP
    by preserving more information and providing learnable, adaptive pooling.
    """

    def __init__(
        self,
        in_channels: int,
        num_clusters: int = 40,  # OPTIMIZED: Reasonable cluster count
        adjacency_threshold: float = 0.3,  # OPTIMIZED: Balanced connectivity
        lstm_hidden_dim: int = 32,  # OPTIMIZED: Simpler capacity
        num_lstm_layers: int = 1,
        use_skip_connections: bool = True,  # Keep: Useful feature
        use_attention_pooling: bool = False,  # DISABLED: Simplify architecture
        use_progressive_pooling: bool = False,  # DISABLED: Fixed cluster count
        initial_clusters: int = 40,  # Same as num_clusters
        use_residual_pooling: bool = False,  # NEW: Residual pooling connections
        use_multi_scale_pooling: bool = False,  # NEW: Multi-scale aggregation
        **kwargs
    ):
        super().__init__()
        
        self.in_channels = in_channels
        self.num_clusters = num_clusters
        self.adjacency_threshold = adjacency_threshold
        self.lstm_hidden_dim = lstm_hidden_dim
        self.num_lstm_layers = num_lstm_layers
        
        # ENHANCED FEATURES
        self.use_skip_connections = use_skip_connections
        self.use_attention_pooling = use_attention_pooling
        self.use_progressive_pooling = use_progressive_pooling
        self.initial_clusters = initial_clusters
        self.current_clusters = initial_clusters if use_progressive_pooling else num_clusters
        self.use_residual_pooling = use_residual_pooling
        self.use_multi_scale_pooling = use_multi_scale_pooling
        
        # LSTM encoder for temporal processing
        self.lstm = nn.LSTM(
            input_size=in_channels,
            hidden_size=lstm_hidden_dim,
            num_layers=num_lstm_layers,
            batch_first=True,
            dropout=0.1 if num_lstm_layers > 1 else 0
        )

        # Pre-create projection layers for advanced pooling (PERFORMANCE FIX)
        if self.use_residual_pooling:
            self.residual_projection = nn.Linear(in_channels, lstm_hidden_dim)

        if self.use_multi_scale_pooling:
            self.scale_projection = nn.Linear(lstm_hidden_dim * 3, lstm_hidden_dim)  # 3 scales
        
        # ENHANCED: Attention-based clustering mechanism
        if self.use_attention_pooling:
            self.cluster_attention = nn.MultiheadAttention(
                embed_dim=lstm_hidden_dim,
                num_heads=4,
                dropout=0.1,
                batch_first=True
            )
            # Learnable cluster embeddings
            self.cluster_embeddings = nn.Parameter(
                torch.randn(self.initial_clusters, lstm_hidden_dim)
            )
            self.cluster_projection = nn.Linear(lstm_hidden_dim, lstm_hidden_dim)
        
        # ENHANCED: Skip connection layers for information bypass
        if self.use_skip_connections:
            self.skip_transform = nn.Sequential(
                nn.Linear(in_channels, lstm_hidden_dim),
                nn.LayerNorm(lstm_hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            )
            self.skip_gate = nn.Sequential(
                nn.Linear(lstm_hidden_dim * 2, lstm_hidden_dim),
                nn.Sigmoid()
            )
        
        # Output layers
        self.output_projection = nn.Linear(lstm_hidden_dim, lstm_hidden_dim)
        self.layer_norm = nn.LayerNorm(lstm_hidden_dim)
        
        # Progressive pooling schedule
        self.training_step = 0
        self.pooling_schedule_length = 1000  # Steps to transition from initial to final clusters

    def update_progressive_pooling(self, step: int):
        """Update cluster count based on training progress."""
        if not self.use_progressive_pooling:
            return
            
        self.training_step = step
        progress = min(1.0, step / self.pooling_schedule_length)
        
        # Smooth transition from initial to final cluster count
        self.current_clusters = int(
            self.initial_clusters - 
            (self.initial_clusters - self.num_clusters) * progress
        )
        self.current_clusters = max(self.num_clusters, self.current_clusters)

    def forward(
        self, x: torch.Tensor, edge_index: torch.Tensor, batch: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Enhanced forward pass with skip connections and attention pooling.
        
        Returns:
            pooled_features: Pooled node features after TSAP
            pooled_edge_index: Super-adjacency edge indices
            auxiliary_features: Features for auxiliary loss (skip connection bypass)
        """
        batch_size = int(batch.max().item()) + 1
        device = x.device
        
        # Store original features for skip connections and residual pooling
        original_features = x.clone() if self.use_skip_connections or self.use_residual_pooling else None

        # Group nodes by batch for processing
        batch_outputs = []
        auxiliary_outputs = []
        
        for b in range(batch_size):
            batch_mask = (batch == b)
            batch_x = x[batch_mask]
            # Select edges where both source and target nodes are in the current batch
            src_nodes = edge_index[0]
            tgt_nodes = edge_index[1]
            batch_node_indices = torch.where(batch_mask)[0]
            src_in_batch = torch.isin(src_nodes, batch_node_indices)
            tgt_in_batch = torch.isin(tgt_nodes, batch_node_indices)
            edge_in_batch = src_in_batch & tgt_in_batch
            batch_edge_index = edge_index[:, edge_in_batch]
            # Adjust edge indices for local batch
            if batch_edge_index.size(1) > 0:
                node_mapping = batch_node_indices
                edge_mapping = torch.zeros(batch.size(0), dtype=torch.long, device=device)
                edge_mapping[node_mapping] = torch.arange(len(node_mapping), device=device)
                batch_edge_index = edge_mapping[batch_edge_index]
            # Process single graph
            pooled_x, aux_x = self._process_single_graph(batch_x, batch_edge_index, device)
            batch_outputs.append(pooled_x)
            auxiliary_outputs.append(aux_x)
        
        # Combine outputs
        pooled_features = torch.cat(batch_outputs, dim=0)
        auxiliary_features = torch.cat(auxiliary_outputs, dim=0)
        
        # Create super-adjacency matrix (simplified for demonstration)
        pooled_edge_index = self._create_super_adjacency(batch_size, device)
        
        return pooled_features, pooled_edge_index, auxiliary_features

    def _process_single_graph(
        self, x: torch.Tensor, edge_index: torch.Tensor, device: torch.device
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Process a single graph with ultra-enhanced TSAP including residual and multi-scale pooling."""
        # Store original features for advanced pooling techniques
        original_features = x.clone()

        # ENHANCED: Skip connection processing for information bypass
        skip_features = None
        if self.use_skip_connections:
            skip_features = self.skip_transform(x)  # Transform original features
        
        # LSTM temporal encoding
        lstm_out, _ = self.lstm(x.unsqueeze(0))  # Add batch dimension
        lstm_features = lstm_out.squeeze(0)  # Remove batch dimension
        
        # ULTRA-ENHANCED: Advanced clustering with multiple techniques
        if self.use_attention_pooling:
            clustered_features = self._attention_based_clustering(lstm_features, device)
        else:
            clustered_features = self._spectral_clustering(lstm_features, edge_index, device)

        # BREAKTHROUGH: Residual pooling to preserve critical information (OPTIMIZED)
        if self.use_residual_pooling and hasattr(self, 'residual_projection'):
            # Use pre-created projection layer (PERFORMANCE FIX)
            # Average pool original features to match cluster count
            avg_pooled_original = F.adaptive_avg_pool1d(
                original_features.t().unsqueeze(0), clustered_features.size(0)
            ).squeeze(0).t()
            residual_features = self.residual_projection(avg_pooled_original)
            clustered_features = clustered_features + 0.3 * residual_features  # Weighted residual

        # BREAKTHROUGH: Multi-scale pooling for comprehensive feature capture (OPTIMIZED)
        if self.use_multi_scale_pooling and hasattr(self, 'scale_projection'):
            # Create multiple pooling scales for richer representation (SIMPLIFIED)
            base_size = clustered_features.size(0)

            # Simplified multi-scale approach for performance
            scale_small = F.adaptive_avg_pool1d(
                lstm_features.t().unsqueeze(0), max(1, base_size // 2)
            ).squeeze(0).t()
            scale_medium = F.adaptive_avg_pool1d(
                lstm_features.t().unsqueeze(0), base_size
            ).squeeze(0).t()
            scale_large = F.adaptive_avg_pool1d(
                lstm_features.t().unsqueeze(0), min(lstm_features.size(0), base_size * 2)
            ).squeeze(0).t()

            # Ensure all scales have the same size
            if scale_small.size(0) != base_size:
                scale_small = F.adaptive_avg_pool1d(scale_small.t().unsqueeze(0), base_size).squeeze(0).t()
            if scale_large.size(0) != base_size:
                scale_large = F.adaptive_avg_pool1d(scale_large.t().unsqueeze(0), base_size).squeeze(0).t()

            # Combine scales efficiently
            combined_features = torch.cat([scale_small, scale_medium, scale_large], dim=1)
            multi_scale_projected = self.scale_projection(combined_features)
            clustered_features = 0.7 * clustered_features + 0.3 * multi_scale_projected
        
        # ENHANCED: Combine with skip connections
        auxiliary_features = skip_features if skip_features is not None else lstm_features
        
        if self.use_skip_connections and skip_features is not None:
            # ENHANCED: Robust skip connection implementation
            target_clusters = self.num_clusters

            # CRITICAL FIX: Proper dimension handling for skip connections
            # Transform skip features to match clustered features dimensions
            if skip_features.size(0) >= target_clusters:
                # Use top-k most important features (by norm)
                feature_norms = torch.norm(skip_features, dim=1)
                _, top_indices = torch.topk(feature_norms, target_clusters)
                skip_slice = skip_features[top_indices]
            else:
                # Pad with learned embeddings instead of zeros
                padding_size = target_clusters - skip_features.size(0)
                # Use mean of existing features for padding
                mean_features = skip_features.mean(dim=0, keepdim=True)
                padding = mean_features.repeat(padding_size, 1)
                skip_slice = torch.cat([skip_features, padding], dim=0)

            # ENHANCED: Ensure feature dimension compatibility
            if skip_slice.size(1) != clustered_features.size(1):
                # Project skip features to match clustered features dimension
                if not hasattr(self, 'skip_projection'):
                    self.skip_projection = nn.Linear(
                        skip_slice.size(1),
                        clustered_features.size(1)
                    ).to(device)
                skip_slice = self.skip_projection(skip_slice)

            # ROBUST: Apply gating mechanism with proper error handling
            try:
                combined = torch.cat([clustered_features, skip_slice], dim=1)
                gate = self.skip_gate(combined)
                clustered_features = gate * clustered_features + (1 - gate) * skip_slice
            except RuntimeError as e:
                print(f"⚠️ Skip connection error: {e}")
                print(f"   Using clustered features only")
                # Fallback: use clustered features without skip connections
        
        # Final processing with dimension validation
        try:
            projected_features = self.output_projection(clustered_features)
            output_features = self.layer_norm(projected_features)
        except RuntimeError as e:
            # Handle dimension mismatch by reshaping if necessary
            print(f"⚠️ Dimension mismatch in TSAP layer norm: {e}")
            print(f"   clustered_features shape: {clustered_features.shape}")
            print(f"   expected lstm_hidden_dim: {self.lstm_hidden_dim}")

            # Fallback: use the clustered features directly without layer norm
            projected_features = self.output_projection(clustered_features)
            output_features = projected_features  # Skip layer norm if dimension mismatch

        return output_features, auxiliary_features

    def _attention_based_clustering(
        self, features: torch.Tensor, device: torch.device
    ) -> torch.Tensor:
        """
        ENHANCED: Attention-based learnable clustering.
        
        Replaces fixed K-means with learnable attention mechanism that can
        adapt to the specific characteristics of financial time series data.
        """
        num_nodes = features.size(0)
        
        # CRITICAL FIX: Always use consistent number of clusters to avoid dimension mismatch
        # Use self.num_clusters instead of self.current_clusters for consistent output
        target_clusters = self.num_clusters
        
        # Ensure we don't exceed available cluster embeddings
        if target_clusters > self.cluster_embeddings.size(0):
            target_clusters = self.cluster_embeddings.size(0)
        
        # Use consistent cluster count across all batches
        current_embeddings = self.cluster_embeddings[:target_clusters]
        
        # Project features to match cluster embedding dimension
        projected_features = self.cluster_projection(features)
        
        # Attention-based soft clustering
        # Query: cluster embeddings, Key/Value: node features
        cluster_attention_out, attention_weights = self.cluster_attention(
            query=current_embeddings.unsqueeze(0),  # [1, target_clusters, hidden_dim]
            key=projected_features.unsqueeze(0),    # [1, num_nodes, hidden_dim]
            value=projected_features.unsqueeze(0)   # [1, num_nodes, hidden_dim]
        )
        
        # Remove batch dimension - shape: [target_clusters, hidden_dim]
        clustered_features = cluster_attention_out.squeeze(0)
        
        return clustered_features

    def _spectral_clustering(
        self, features: torch.Tensor, edge_index: torch.Tensor, device: torch.device
    ) -> torch.Tensor:
        """Fallback spectral clustering implementation."""
        num_nodes = features.size(0)
        
        # CRITICAL FIX: Use consistent cluster count to avoid dimension mismatch
        target_clusters = self.num_clusters
        
        # Simple fallback: average pooling with reduced dimensionality
        if num_nodes <= target_clusters:
            # Pad if too few nodes
            padding = torch.zeros(
                target_clusters - num_nodes, 
                features.size(1), 
                device=device
            )
            return torch.cat([features, padding], dim=0)
        
        # OPTIMIZED: Fast graph-aware clustering (PERFORMANCE FIX)
        if edge_index.size(1) > 0:
            # Fast degree computation without full adjacency matrix
            node_degrees = torch.zeros(num_nodes, device=device)
            node_degrees.scatter_add_(0, edge_index[0], torch.ones(edge_index.size(1), device=device))
            node_degrees.scatter_add_(0, edge_index[1], torch.ones(edge_index.size(1), device=device))

            # Add self-loops for stability
            node_degrees += 1.0

            # Compute importance weights efficiently
            importance_weights = torch.softmax(node_degrees, dim=0)
            weighted_features = features * importance_weights.unsqueeze(1)
        else:
            # Use uniform weights when no edges are available
            importance_weights = torch.ones(num_nodes, device=device) / num_nodes
            weighted_features = features

        # IMPROVED: Feature similarity-based clustering
        nodes_per_cluster = num_nodes // target_clusters
        clustered_features = []

        # Sort nodes by feature magnitude for better clustering
        feature_norms = torch.norm(weighted_features, dim=1)
        sorted_indices = torch.argsort(feature_norms, descending=True)

        for i in range(target_clusters):
            start_idx = i * nodes_per_cluster
            if i == target_clusters - 1:  # Last cluster gets remaining nodes
                end_idx = num_nodes
            else:
                end_idx = (i + 1) * nodes_per_cluster

            # Use sorted indices for better feature distribution
            cluster_indices = sorted_indices[start_idx:end_idx]
            cluster_nodes = weighted_features[cluster_indices]

            # Use weighted average instead of simple mean
            cluster_weights = importance_weights[cluster_indices] if edge_index.size(1) > 0 else torch.ones(cluster_nodes.size(0), device=device)
            cluster_weights = cluster_weights / cluster_weights.sum()

            cluster_feature = (cluster_nodes * cluster_weights.unsqueeze(1)).sum(dim=0)
            clustered_features.append(cluster_feature)

        return torch.stack(clustered_features)

    def _create_super_adjacency(self, batch_size: int, device: torch.device) -> torch.Tensor:
        """Create super-adjacency matrix for pooled graphs."""
        # CRITICAL FIX: Use consistent cluster count to avoid dimension mismatch
        target_clusters = self.num_clusters
        # total_clusters = batch_size * target_clusters  # Not currently used
        
        # Simplified: create connections within each graph's clusters
        edges = []
        for b in range(batch_size):
            base_idx = b * target_clusters
            for i in range(target_clusters):
                for j in range(i + 1, target_clusters):
                    edges.append([base_idx + i, base_idx + j])
                    edges.append([base_idx + j, base_idx + i])
        
        if not edges:
            return torch.empty((2, 0), dtype=torch.long, device=device)
        
        return torch.tensor(edges, dtype=torch.long, device=device).t()


class TSAPGnnLSTM(nn.Module):
    """
    Enhanced TSAP-GNN-LSTM model with Information-Preserving Architecture.
    
    ENHANCED FEATURES:
    - Integration with TSAP-Enhanced pooling
    - Auxiliary classifier for multi-objective training
    - Progressive pooling support during training
    - Skip connection integration
    - Multi-objective loss computation
    
    This enhanced version addresses performance gaps by combining TSAP pooling
    with auxiliary objectives and information bypass mechanisms.
    """

    def __init__(
        self,
        node_feature_dim: int,
        lstm_hidden_dim: int = 48,  # ENHANCED: Balanced capacity
        gnn_hidden_dim: int = 96,   # ENHANCED: Good representational power
        num_gnn_layers: int = 2,    # ENHANCED: Restored depth
        num_heads: int = 4,         # ENHANCED: Adequate attention
        dropout_rate: float = 0.3,  # ENHANCED: Balanced regularization
        num_clusters: int = 70,     # ENHANCED: Minimal compression
        adjacency_threshold: float = 0.15,  # ENHANCED: Ultra-high connectivity
        gnn_type: str = "gat",
        use_residual: bool = True,
        use_layer_norm: bool = True,
        graph_feature_dim: int = 0,
        # SIMPLIFIED FEATURES
        use_skip_connections: bool = True,
        use_attention_pooling: bool = False,  # DISABLED
        use_auxiliary_loss: bool = False,     # DISABLED
        auxiliary_loss_weight: float = 0.0,
        use_progressive_pooling: bool = False, # DISABLED
        initial_clusters: int = 40,
        use_residual_pooling: bool = False,  # NEW: Residual pooling connections
        use_multi_scale_pooling: bool = False,  # NEW: Multi-scale aggregation
        **kwargs,
    ):
        super().__init__()

        # Suppress unused parameter warnings
        _ = kwargs

        # Store enhanced configuration
        self.node_feature_dim = node_feature_dim
        self.lstm_hidden_dim = lstm_hidden_dim
        self.gnn_hidden_dim = gnn_hidden_dim
        self.num_gnn_layers = num_gnn_layers
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate
        self.num_clusters = num_clusters
        self.adjacency_threshold = adjacency_threshold
        self.gnn_type = gnn_type.lower()
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm
        self.graph_feature_dim = graph_feature_dim
        
        # ENHANCED FEATURES
        self.use_skip_connections = use_skip_connections
        self.use_attention_pooling = use_attention_pooling
        self.use_auxiliary_loss = use_auxiliary_loss
        self.auxiliary_loss_weight = auxiliary_loss_weight
        self.use_progressive_pooling = use_progressive_pooling
        self.initial_clusters = initial_clusters
        self.use_residual_pooling = use_residual_pooling
        self.use_multi_scale_pooling = use_multi_scale_pooling
        
        # Enhanced TSAP module
        self.tsap = TemporalSpectralAttentionPooling(
            in_channels=node_feature_dim,
            num_clusters=num_clusters,
            adjacency_threshold=adjacency_threshold,
            lstm_hidden_dim=lstm_hidden_dim,
            use_skip_connections=use_skip_connections,
            use_attention_pooling=use_attention_pooling,
            use_progressive_pooling=use_progressive_pooling,
            initial_clusters=initial_clusters,
            use_residual_pooling=use_residual_pooling,
            use_multi_scale_pooling=use_multi_scale_pooling,
        )
        
        # GNN layers operating on pooled features
        self.gnn_layers = nn.ModuleList()
        
        # Input projection from TSAP to GNN
        input_dim = lstm_hidden_dim
        self.input_projection = nn.Linear(input_dim, gnn_hidden_dim)
        
        # Create GNN layers
        for i in range(num_gnn_layers):
            layer_input_dim = gnn_hidden_dim
            layer = self._create_gnn_layer(layer_input_dim, gnn_hidden_dim, gnn_type, num_heads)
            self.gnn_layers.append(layer)
            
            if use_layer_norm:
                self.gnn_layers.append(nn.LayerNorm(gnn_hidden_dim))
            
            if i < num_gnn_layers - 1:  # No dropout after last layer
                self.gnn_layers.append(nn.Dropout(dropout_rate))
        
        # Global pooling and classifier
        pooled_dim = gnn_hidden_dim
        if graph_feature_dim > 0:
            pooled_dim += graph_feature_dim
            
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # Main classifier
        self.classifier = self._create_classifier(
            pooled_dim, gnn_hidden_dim, dropout_rate, use_layer_norm
        )
        
        # ENHANCED: Auxiliary classifier for multi-objective training
        if self.use_auxiliary_loss:
            # Auxiliary classifier operates on skip connection features
            self.auxiliary_classifier = nn.Sequential(
                nn.Linear(lstm_hidden_dim, gnn_hidden_dim // 2),
                nn.LayerNorm(gnn_hidden_dim // 2) if use_layer_norm else nn.Identity(),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(gnn_hidden_dim // 2, gnn_hidden_dim // 4),
                nn.LayerNorm(gnn_hidden_dim // 4) if use_layer_norm else nn.Identity(),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(gnn_hidden_dim // 4, 1)
            )
        
        # Initialize weights
        self._initialize_weights()
        
        # Training step counter for progressive pooling
        self.training_step = 0

    def update_training_step(self, step: int):
        """Update training step for progressive pooling."""
        self.training_step = step
        if self.use_progressive_pooling:
            self.tsap.update_progressive_pooling(step)

    def forward(self, data) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Enhanced forward pass with auxiliary loss support.
        
        Returns:
            If use_auxiliary_loss=False: main prediction tensor
            If use_auxiliary_loss=True: tuple of (main_prediction, auxiliary_prediction)
        """
        # Input validation
        if not hasattr(data, 'x') or not hasattr(data, 'edge_index') or not hasattr(data, 'batch'):
            raise ValueError("Input data must have 'x', 'edge_index', and 'batch' attributes")
        
        x, edge_index, batch = data.x, data.edge_index, data.batch
        device = x.device
        
        # Calculate batch size
        batch_size = batch.max().item() + 1
        
        # TSAP processing
        pooled_features, pooled_edge_index, auxiliary_features = self.tsap(x, edge_index, batch)
        
        # Apply input projection to transform from TSAP output to GNN input dimensions
        gnn_out = self.input_projection(pooled_features)
        
        # GNN processing with proper layer handling
        layer_idx = 0
        for i in range(self.num_gnn_layers):
            # Apply GNN layer
            gnn_out = self.gnn_layers[layer_idx](gnn_out, pooled_edge_index)
            layer_idx += 1
            
            # Apply layer normalization if enabled
            if self.use_layer_norm and layer_idx < len(self.gnn_layers):
                if isinstance(self.gnn_layers[layer_idx], nn.LayerNorm):
                    gnn_out = self.gnn_layers[layer_idx](gnn_out)
                    layer_idx += 1
            
            # Apply activation and dropout (except for last layer)
            if i < self.num_gnn_layers - 1:
                gnn_out = torch.relu(gnn_out)
                
                # Apply dropout if present
                if layer_idx < len(self.gnn_layers) and isinstance(self.gnn_layers[layer_idx], nn.Dropout):
                    gnn_out = self.gnn_layers[layer_idx](gnn_out)
                    layer_idx += 1
        
        # Global pooling - adjusted for pooled features from TSAP
        # After TSAP, we have pooled features with different batch structure
        # We need to create batch assignments for the pooled features
        
        # Since TSAP processes each batch separately and returns concatenated results,
        # we need to determine how many pooled nodes each batch contributed
        pooled_gnn = []
        
        # Calculate how many nodes each batch contributed after pooling
        nodes_per_batch = []
        current_idx = 0
        for b in range(batch_size):
            # For TSAP, each batch contributes num_clusters nodes
            batch_nodes = self.num_clusters  # Each batch contributes this many pooled nodes
            nodes_per_batch.append(batch_nodes)
        
        # Extract pooled features for each batch
        current_idx = 0
        for b in range(batch_size):
            batch_node_count = nodes_per_batch[b]
            if batch_node_count > 0:
                # Extract the pooled nodes for this batch
                end_idx = current_idx + batch_node_count
                if end_idx <= gnn_out.size(0):
                    batch_pooled_features = gnn_out[current_idx:end_idx]
                    # Average pool the features for this batch
                    batch_representation = batch_pooled_features.mean(dim=0)
                    pooled_gnn.append(batch_representation)
                else:
                    # Fallback if indexing is wrong
                    pooled_gnn.append(torch.zeros(self.gnn_hidden_dim, device=device))
                current_idx = end_idx
            else:
                # Empty batch case
                pooled_gnn.append(torch.zeros(self.gnn_hidden_dim, device=device))
        
        if pooled_gnn:
            pooled_gnn = torch.stack(pooled_gnn)
        else:
            pooled_gnn = torch.zeros(batch_size, self.gnn_hidden_dim, device=device)
        
        # Use the globally pooled GNN features as the main representation
        # pooled_gnn already contains the processed information from TSAP -> GNN -> Global Pooling
        final_features = pooled_gnn
        
        # Add graph-level features if available
        if self.graph_feature_dim > 0 and hasattr(data, 'graph_features') and data.graph_features is not None:
            graph_features = data.graph_features
            
            # Ensure graph features are properly formatted
            if not torch.is_tensor(graph_features):
                graph_features = torch.tensor(graph_features, dtype=torch.float32, device=device)
            
            graph_features = graph_features.to(device)
            
            # Handle different graph feature dimensions
            if graph_features.dim() == 1:
                # Single feature vector - need to reshape appropriately
                if graph_features.size(0) == self.graph_feature_dim:
                    # Perfect size - expand to match batch size
                    graph_features = graph_features.unsqueeze(0).expand(batch_size, -1)
                elif graph_features.size(0) == batch_size:
                    # This might be a flattened batch - reshape to [batch_size, 1]
                    graph_features = graph_features.unsqueeze(1)
                else:
                    # Unknown size - create proper shape with zeros and use what we have
                    new_features = torch.zeros(batch_size, self.graph_feature_dim, device=device)
                    # Use as much as we can from the original features
                    min_size = min(graph_features.size(0), self.graph_feature_dim)
                    new_features[0, :min_size] = graph_features[:min_size]
                    graph_features = new_features
            elif graph_features.dim() == 2:
                # Batch of feature vectors
                if graph_features.size(0) != batch_size:
                    if graph_features.size(0) == 1:
                        graph_features = graph_features.expand(batch_size, -1)
                    else:
                        # Truncate or pad to match batch size
                        if graph_features.size(0) > batch_size:
                            graph_features = graph_features[:batch_size]
                        else:
                            # Pad with zeros or repeat last row
                            padding_needed = batch_size - graph_features.size(0)
                            last_row = graph_features[-1:].expand(padding_needed, -1)
                            graph_features = torch.cat([graph_features, last_row], dim=0)
            else:
                # Higher dimensional tensors - flatten and reshape
                graph_features = graph_features.view(batch_size, -1)
            
            # Ensure correct feature dimension (now guaranteed to be 2D)
            if graph_features.size(1) != self.graph_feature_dim:
                # Truncate or pad feature dimension
                if graph_features.size(1) > self.graph_feature_dim:
                    graph_features = graph_features[:, :self.graph_feature_dim]
                else:
                    # Pad with zeros
                    padding_needed = self.graph_feature_dim - graph_features.size(1)
                    padding = torch.zeros(batch_size, padding_needed, device=device)
                    graph_features = torch.cat([graph_features, padding], dim=1)
            
            final_features = torch.cat([final_features, graph_features], dim=1)
        
        # Dynamic classifier adjustment if needed
        actual_input_dim = final_features.size(1)
        current_classifier_input_dim = None
        
        if hasattr(self, 'classifier') and self.classifier is not None:
            if len(self.classifier) > 0 and hasattr(self.classifier[0], 'in_features'):
                current_classifier_input_dim = self.classifier[0].in_features
            
            if current_classifier_input_dim is not None and actual_input_dim != current_classifier_input_dim:
                # Recreate classifier with correct input dimension
                self.classifier = self._create_classifier(
                    actual_input_dim, 
                    self.gnn_hidden_dim, 
                    self.dropout_rate, 
                    self.use_layer_norm
                )
        
        main_prediction = self.classifier(final_features)
        
        # ENHANCED: Handle auxiliary loss if enabled
        if self.use_auxiliary_loss and self.training:
            # Pool auxiliary features to graph-level
            # auxiliary_features are node-level [total_nodes, feature_dim]
            # We need to pool them to graph-level [batch_size, feature_dim]
            
            pooled_auxiliary_features = []
            current_node_idx = 0
            
            for b in range(batch_size):
                batch_mask = (batch == b)
                num_nodes_in_batch = batch_mask.sum().item()
                
                if num_nodes_in_batch > 0:
                    # Get auxiliary features for this batch
                    batch_aux_features = auxiliary_features[current_node_idx:current_node_idx + num_nodes_in_batch]
                    # Pool to single vector
                    pooled_batch_aux = batch_aux_features.mean(dim=0)
                    pooled_auxiliary_features.append(pooled_batch_aux)
                    current_node_idx += num_nodes_in_batch
                else:
                    # Empty batch - create zero features
                    pooled_auxiliary_features.append(torch.zeros(auxiliary_features.size(1), device=device))
            
            # Stack all batch features
            pooled_auxiliary_features = torch.stack(pooled_auxiliary_features)
            
            # Apply auxiliary classifier
            auxiliary_prediction = self.auxiliary_classifier(pooled_auxiliary_features)
            
            return main_prediction, auxiliary_prediction
        
        return main_prediction

    def compute_enhanced_loss(self, predictions, targets, criterion):
        """
        Compute enhanced loss with auxiliary objectives.
        
        Args:
            predictions: Model predictions (tuple if auxiliary loss enabled)
            targets: Ground truth targets
            criterion: Base loss criterion
            
        Returns:
            Total loss combining main and auxiliary objectives
        """
        if self.use_auxiliary_loss and isinstance(predictions, tuple):
            main_pred, aux_pred = predictions
            
            # CRITICAL FIX: Handle shape mismatches
            # Ensure predictions and targets have compatible shapes
            if main_pred.dim() == 2 and main_pred.size(1) == 1:
                main_pred = main_pred.squeeze(1)  # [batch_size, 1] -> [batch_size]
            if aux_pred.dim() == 2 and aux_pred.size(1) == 1:
                aux_pred = aux_pred.squeeze(1)  # [batch_size, 1] -> [batch_size]
                
            # Ensure targets are 1D
            if targets.dim() == 2:
                targets = targets.squeeze(1)
            
            # Validate shapes match
            if main_pred.shape != targets.shape:
                raise ValueError(f"Shape mismatch after correction: main_pred={main_pred.shape}, targets={targets.shape}")
            if aux_pred.shape != targets.shape:
                raise ValueError(f"Shape mismatch after correction: aux_pred={aux_pred.shape}, targets={targets.shape}")
            
            # Main loss
            main_loss = criterion(main_pred, targets)
            
            # Auxiliary loss (same targets)
            aux_loss = criterion(aux_pred, targets)
            
            # Combined loss
            total_loss = (1 - self.auxiliary_loss_weight) * main_loss + \
                        self.auxiliary_loss_weight * aux_loss
            
            return total_loss, main_loss, aux_loss
        else:
            # Standard loss
            if isinstance(predictions, tuple):
                predictions = predictions[0]  # Use main prediction
            
            # CRITICAL FIX: Handle shape mismatches for standard loss
            if predictions.dim() == 2 and predictions.size(1) == 1:
                predictions = predictions.squeeze(1)  # [batch_size, 1] -> [batch_size]
            
            # Ensure targets are 1D
            if targets.dim() == 2:
                targets = targets.squeeze(1)
            
            # Validate shapes match
            if predictions.shape != targets.shape:
                raise ValueError(f"Shape mismatch after correction: predictions={predictions.shape}, targets={targets.shape}")
            
            main_loss = criterion(predictions, targets)
            return main_loss, main_loss, torch.tensor(0.0, device=main_loss.device)

    def _create_classifier(self, input_dim: int, hidden_dim: int, dropout_rate: float, use_layer_norm: bool):
        """
        Create classifier with improved architecture to prevent collapse.
        
        Args:
            input_dim: Input dimension
            hidden_dim: Hidden dimension
            dropout_rate: Dropout rate
            use_layer_norm: Whether to use layer normalization
            
        Returns:
            Classifier module
        """
        layers = []
        
        # First layer with more capacity
        layers.append(nn.Linear(input_dim, hidden_dim * 2))
        if use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dim * 2))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))
        
        # Second layer 
        layers.append(nn.Linear(hidden_dim * 2, hidden_dim))
        if use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))
        
        # Output layer - important: don't use activation here (will be handled by loss function)
        layers.append(nn.Linear(hidden_dim, 1))
        
        classifier = nn.Sequential(*layers)
        
        # Improved weight initialization to prevent collapse
        def init_weights(m):
            if isinstance(m, nn.Linear):
                # Xavier initialization for better gradient flow
                torch.nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    # Small positive bias to encourage diverse predictions
                    torch.nn.init.constant_(m.bias, 0.01)
        
        classifier.apply(init_weights)
        
        return classifier

    def _initialize_weights(self):
        """
        Initialize model weights to prevent collapse and ensure good gradient flow.
        """
        def init_module(m):
            if isinstance(m, nn.Linear):
                # Xavier initialization for linear layers
                torch.nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    if m.out_features == 1:  # Output layer
                        # Small positive bias for output layer to encourage diverse predictions
                        torch.nn.init.constant_(m.bias, 0.01)
                    else:
                        # Zero bias for hidden layers
                        torch.nn.init.constant_(m.bias, 0.0)
            elif isinstance(m, nn.LSTM):
                # Initialize LSTM weights
                for name, param in m.named_parameters():
                    if 'weight_ih' in name:
                        torch.nn.init.xavier_uniform_(param.data)
                    elif 'weight_hh' in name:
                        torch.nn.init.orthogonal_(param.data)
                    elif 'bias' in name:
                        torch.nn.init.constant_(param.data, 0.0)
            elif isinstance(m, nn.LayerNorm):
                torch.nn.init.constant_(m.bias, 0.0)
                torch.nn.init.constant_(m.weight, 1.0)
            elif isinstance(m, nn.Embedding):
                torch.nn.init.normal_(m.weight, mean=0.0, std=0.1)
        
        # Apply initialization to the entire model
        self.apply(init_module)
        
        # Special initialization for TSAP module
        if hasattr(self, 'tsap'):
            self.tsap.apply(init_module)
        
        # Special initialization for GNN layers
        # Optimized initialization for GNN layers
        for layer in self.gnn_layers:
            # Prefer reset_parameters if available (PyG layers)
            if hasattr(layer, 'reset_parameters') and callable(layer.reset_parameters):
                layer.reset_parameters()
            else:
                # Fallback: apply custom initialization
                layer.apply(init_module)
        
        # Initialize classifier and auxiliary classifier
        if hasattr(self, 'classifier') and isinstance(self.classifier, nn.Module):
            self.classifier.apply(init_module)
        
        if hasattr(self, 'auxiliary_classifier') and isinstance(self.auxiliary_classifier, nn.Module):
            self.auxiliary_classifier.apply(init_module)

    def _create_gnn_layer(self, input_dim: int, hidden_dim: int, gnn_type: str, num_heads: int):
        """Create a GNN layer based on the specified type and parameters."""
        if gnn_type == "gat":
            from torch_geometric.nn import GATConv
            return GATConv(input_dim, hidden_dim, heads=num_heads, dropout=self.dropout_rate, concat=False)
        elif gnn_type == "transformer":
            from torch_geometric.nn import TransformerConv
            return TransformerConv(input_dim, hidden_dim, heads=num_heads, dropout=self.dropout_rate, concat=False)
        elif gnn_type == "gcn":
            from torch_geometric.nn import GraphConv
            return GraphConv(input_dim, hidden_dim)
        else:
            raise ValueError(f"Unsupported GNN type: {gnn_type}")


class PlainGNN(nn.Module):
    """
    Plain GNN baseline model without TSAP pooling.
    
    This model serves as a baseline comparison to the TSAP-enhanced model.
    It directly processes the original graph structure with the same architectural
    enhancements: dropout, residual connections, and layer normalization.
    
    Args:
        node_feature_dim: Dimension of input node features (sequence length)
        gnn_hidden_dim: Hidden dimension for GNN layers
        num_gnn_layers: Number of GNN layers
        num_heads: Number of attention heads (for GAT/Transformer)
        dropout_rate: Dropout probability
        gnn_type: Type of GNN layer ('gat', 'transformer', 'gcn')
        use_residual: Enable residual connections
        use_layer_norm: Enable layer normalization
        pooling_type: Global pooling type ('mean' or 'max')
        graph_feature_dim: Dimension of graph-level features
    """

    def __init__(
        self,
        node_feature_dim: int,
        gnn_hidden_dim: int = 128,
        num_gnn_layers: int = 2,
        num_heads: int = 4,
        dropout_rate: float = 0.1,
        gnn_type: str = "gat",
        use_residual: bool = True,
        use_layer_norm: bool = True,
        pooling_type: str = "mean",
        graph_feature_dim: int = 0,
        **kwargs,
    ):
        super().__init__()

        # Suppress unused parameter warnings
        _ = kwargs
        
        # Store all parameters as instance variables for later use
        self.node_feature_dim = node_feature_dim
        self.gnn_hidden_dim = gnn_hidden_dim
        self.num_gnn_layers = num_gnn_layers
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate
        self.gnn_type = gnn_type
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm
        self.pooling_type = pooling_type
        self.graph_feature_dim = graph_feature_dim

        # Input projection for temporal features
        self.input_projection = nn.Linear(node_feature_dim, gnn_hidden_dim)

        # GNN layers
        self.gnn_layers = nn.ModuleList()
        self.gnn_norms = nn.ModuleList()
        self.residual_projections = nn.ModuleList()

        # Select GNN layer type
        gnn_constructor = {
            "gat": GATConv,
            "transformer": TransformerConv,
            "gcn": GraphConv,
        }.get(gnn_type, GATConv)

        # Build GNN layers
        for i in range(num_gnn_layers):
            if i == 0:
                # First layer
                in_dim = gnn_hidden_dim
                out_dim = gnn_hidden_dim
            else:
                # Subsequent layers
                in_dim = gnn_hidden_dim * num_heads
                out_dim = gnn_hidden_dim

            # Add GNN layer
            if gnn_type in ["gat", "transformer"]:
                self.gnn_layers.append(
                    gnn_constructor(in_dim, out_dim, heads=num_heads, dropout=dropout_rate)
                )
            else:
                self.gnn_layers.append(gnn_constructor(in_dim, out_dim))

            # Add normalization layer
            norm_dim = out_dim * num_heads if gnn_type in ["gat", "transformer"] else out_dim
            if use_layer_norm:
                self.gnn_norms.append(nn.LayerNorm(norm_dim))
            else:
                self.gnn_norms.append(nn.Identity())

            # Add residual projection if needed
            if use_residual and in_dim != norm_dim:
                self.residual_projections.append(nn.Linear(in_dim, norm_dim))
            else:
                self.residual_projections.append(nn.Identity())

        # Global pooling
        if pooling_type == "mean":
            self.pooling = global_mean_pool
        elif pooling_type == "max":
            self.pooling = global_max_pool
        else:
            raise ValueError(f"Unsupported pooling type: {pooling_type}")

        # Enhanced classifier with dynamic input dimension handling
        final_dim = (
            gnn_hidden_dim * num_heads
            if gnn_type in ["gat", "transformer"]
            else gnn_hidden_dim
        )
        
        # Store the base embedding dimension for dynamic classifier creation
        self.base_embedding_dim = int(final_dim)
        self.graph_feature_dim = int(graph_feature_dim)
        self.expected_graph_feature_dim = int(graph_feature_dim)
        
        # Initialize classifier with base dimension only
        # The classifier will be dynamically recreated if graph features are encountered
        # with different dimensions than expected
        initial_classifier_input_dim = final_dim + (graph_feature_dim if graph_feature_dim > 0 else 0)
        self._create_classifier(initial_classifier_input_dim, gnn_hidden_dim, dropout_rate, use_layer_norm)

    def _create_classifier(self, input_dim: int, hidden_dim: int, dropout_rate: float, use_layer_norm: bool):
        """Create or recreate the classifier with improved architecture to prevent prediction concentration."""
        # Suppress unused parameter warning
        _ = hidden_dim
        # IMPROVED: Less aggressive dimensionality reduction to prevent bottlenecks
        # Use larger intermediate dimensions and add batch normalization for better training dynamics
        hidden_dim_1 = max(128, input_dim // 2)  # Larger first hidden layer
        hidden_dim_2 = max(64, input_dim // 4)   # Less aggressive reduction
        
        classifier_layers = [
            # First block - maintain more capacity
            nn.Linear(input_dim, hidden_dim_1),
            nn.BatchNorm1d(hidden_dim_1),  # Add batch norm for training stability
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            # Second block - moderate reduction
            nn.Linear(hidden_dim_1, hidden_dim_2),
            nn.BatchNorm1d(hidden_dim_2),
            nn.ReLU(), 
            nn.Dropout(dropout_rate),
            
            # Final layer - no activation to allow full range
            nn.Linear(hidden_dim_2, 1),
        ]
        
        if use_layer_norm:
            # Add LayerNorm before the final layer for additional regularization
            classifier_layers.insert(-1, nn.LayerNorm(hidden_dim_2))
        
        self.classifier = nn.Sequential(*classifier_layers)

    def _initialize_weights(self):
        """Enhanced weight initialization for better prediction diversity."""
        print("   🎲 Initializing PlainGNN weights for prediction diversity...")
        
        for name, module in self.named_modules():
            if isinstance(module, nn.Linear):
                # Enhanced initialization for better diversity
                if 'classifier' in name:
                    # Use He initialization for ReLU networks with diversity-encouraging bias
                    nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                    if module.bias is not None:
                        # Initialize bias with diverse values to encourage different starting points
                        nn.init.uniform_(module.bias, -0.2, 0.2)
                        print(f"   • Classifier layer '{name}': Kaiming normal + diverse uniform bias")
                else:
                    # Standard initialization for other linear layers
                    nn.init.xavier_normal_(module.weight, gain=1.2)  # Slightly higher gain
                    if module.bias is not None:
                        nn.init.zeros_(module.bias)
            
            elif isinstance(module, nn.BatchNorm1d):
                # Proper batch norm initialization
                if module.weight is not None:
                    nn.init.ones_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
                print(f"   • BatchNorm layer '{name}': Standard initialization")
            
            elif hasattr(module, 'weight') and module.weight is not None:
                # Enhanced initialization for other layers with weights
                if isinstance(module.weight, torch.Tensor) and module.weight.dim() >= 2:
                    nn.init.xavier_normal_(module.weight, gain=1.2)  # Slightly higher gain
                    if hasattr(module, 'bias') and module.bias is not None and isinstance(module.bias, torch.Tensor):
                        nn.init.zeros_(module.bias)
        
        print("   ✅ PlainGNN weight initialization completed for enhanced diversity")

    def forward(self, data) -> torch.Tensor:
        """
        Forward pass of PlainGNN model.
        Now supports graph-level features (data.graph_features) concatenated to the pooled embedding before classification.
        --- ATTENTION BATCH HANDLING NOTE ---
        Attention (GAT/Transformer) layers must NEVER receive batch as input; batch is only for pooling.
        Node/edge indices must not be mixed. This is enforced by runtime assertions below.
        """
        # CRITICAL FIX: Add comprehensive dimension diagnostics at start
        print(f"🔍 PlainGNN Forward Pass - Input Diagnostics:")
        
        # Extract data components with comprehensive validation
        node_features = data.x
        edge_index = data.edge_index
        edge_attr = getattr(data, 'edge_attr', None)
        batch = getattr(data, 'batch', None)
        graph_features = getattr(data, 'graph_features', None)
        
        # Log all input dimensions
        print(f"   • Node features: {node_features.shape if node_features is not None else 'None'}")
        print(f"   • Edge index: {edge_index.shape if edge_index is not None else 'None'}")
        print(f"   • Edge attr: {edge_attr.shape if edge_attr is not None else 'None'}")
        print(f"   • Batch: {batch.shape if batch is not None else 'None'}")
        print(f"   • Graph features: {graph_features.shape if graph_features is not None else 'None'}")
        
        # Log model configuration
        print(f"   • Model config: node_dim={self.node_feature_dim}, graph_dim={self.graph_feature_dim}")
        print(f"   • GNN config: hidden={self.gnn_hidden_dim}, layers={self.num_gnn_layers}, heads={self.num_heads}")
        
        # Comprehensive tensor validation for PlainGNN
        if node_features is None or edge_index is None:
            raise ValueError("PlainGNN requires node_features and edge_index")
        
        if not torch.is_tensor(node_features) or not torch.is_tensor(edge_index):
            raise ValueError("PlainGNN input must be tensors")
        
        # Validate tensor dimensions
        if node_features.dim() != 2:
            raise ValueError(f"Node features must be 2D, got shape {node_features.shape}")
        
        if edge_index.dim() != 2 or edge_index.size(0) != 2:
            raise ValueError(f"Edge index must have shape [2, num_edges], got {edge_index.shape}")
        
        # Validate input dimension matches model expectation
        actual_node_dim = node_features.shape[1]
        if actual_node_dim != self.node_feature_dim:
            raise ValueError(f"Node feature dimension mismatch: model expects {self.node_feature_dim}, got {actual_node_dim}")
        
        # Validate batch tensor if present
        if batch is not None:
            if not torch.is_tensor(batch):
                raise ValueError("Batch must be a tensor")
            
            if batch.dim() != 1:
                raise ValueError(f"Batch must be 1D, got shape {batch.shape}")
            
            if batch.size(0) != node_features.size(0):
                raise ValueError(f"Batch-node size mismatch: batch={batch.size(0)}, nodes={node_features.size(0)}")
            
            # Validate batch values
            if batch.min() < 0:
                raise ValueError(f"Batch contains negative values: min={batch.min()}")
        
        # Validate edge indices are within bounds
        if edge_index.numel() > 0:
            max_edge_idx = safe_item(edge_index.max())
            min_edge_idx = safe_item(edge_index.min())
            num_nodes = node_features.size(0)
            if max_edge_idx >= num_nodes or min_edge_idx < 0:
                raise ValueError(f"Edge indices out of bounds: [{min_edge_idx}, {max_edge_idx}] for {num_nodes} nodes")
        
        print(f"   ✅ PlainGNN validation passed: nodes={node_features.shape}, edges={edge_index.shape}, batch={batch.shape if batch is not None else None}")
        
        x = node_features

        # Handle empty graphs
        if x.size(0) == 0:
            return torch.zeros((data.num_graphs, 1), device=x.device)

        # Project input features with dimension logging
        print(f"   🔄 Input projection: {x.shape} -> linear({self.node_feature_dim}, {self.gnn_hidden_dim})")
        x = self.input_projection(x)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout_rate, training=self.training)
        print(f"   📏 After input projection: {x.shape}")

        # Apply GNN layers with enhancements
        for i, gnn in enumerate(self.gnn_layers):
            assert x.dim() == 2, f"GNN input must be 2D, got {x.shape}"
            assert edge_index.dim() == 2 and edge_index.size(0) == 2, f"edge_index must be [2, num_edges], got {edge_index.shape}"
            # Do NOT pass batch to GNN/attention layers; only to pooling.
            x_prev = x
            x = gnn(x, edge_index)
            x = self.gnn_norms[i](x)
            if self.use_residual:
                residual = self.residual_projections[i](x_prev)
                # Ensure residual is properly typed as tensor for addition
                residual_tensor: torch.Tensor = residual if isinstance(residual, torch.Tensor) else torch.tensor(residual)
                # Use torch.add to resolve Pylance type issues
                x = torch.add(x, residual_tensor)
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout_rate, training=self.training)

        # Global pooling with enhanced validation
        # Validate that batch tensor matches node tensor dimensions
        if batch is not None:
            if batch.size(0) != x.size(0):
                raise ValueError(f"Batch tensor size mismatch: batch={batch.size(0)}, nodes={x.size(0)}")
            
            # Additional validation for batch tensor values
            if batch.min() < 0:
                raise ValueError(f"Batch tensor contains negative values: min={batch.min()}")
            
            # Ensure batch tensor is on the same device as x
            if batch.device != x.device:
                batch = batch.to(x.device)
            
            # Validate batch tensor type
            if batch.dtype != torch.long:
                batch = batch.long()
        
        # Apply global pooling with proper error handling
        graph_embedding = self.pooling(x, batch)

        # Handle graph-level features with dynamic classifier adjustment
        if hasattr(data, 'graph_features') and data.graph_features is not None:
            graph_features = data.graph_features
            batch_size = graph_embedding.size(0)
            
            print(f"   • Processing graph features: {graph_features.shape}")
            if not torch.is_tensor(graph_features):
                graph_features = torch.tensor(graph_features, dtype=torch.float32, device=graph_embedding.device)
            graph_features = graph_features.to(graph_embedding.device)
            if torch.isnan(graph_features).any():
                print(f"⚠️ Warning: NaN values in graph features")
                graph_features = torch.nan_to_num(graph_features, nan=0.0)
            
            expected_batch_size = batch_size
            expected_feature_dim = 16  # Fixed graph feature dimension
            
            print(f"   • Expected batch_size: {expected_batch_size}, feature_dim: {expected_feature_dim}")
            print(f"   • Raw graph_features shape: {graph_features.shape}")
            
            # --- FIX: Handle PyTorch Geometric batching behavior ---
            # PyG concatenates graph_features instead of stacking them
            # So [batch_size] samples with [16] features each become [batch_size * 16] instead of [batch_size, 16]
            
            if graph_features.dim() == 1:
                # 1D tensor - check if it's concatenated features from PyG batching
                total_size = graph_features.size(0)
                if total_size == expected_batch_size * expected_feature_dim:
                    # This is the PyG concatenation case: reshape [batch_size * 16] -> [batch_size, 16]
                    print(f"   • Detected PyG concatenated features: reshaping {total_size} -> [{expected_batch_size}, {expected_feature_dim}]")
                    graph_features = graph_features.view(expected_batch_size, expected_feature_dim)
                elif total_size == expected_feature_dim:
                    # Single sample features: expand to batch
                    print(f"   • Single sample features: expanding [{expected_feature_dim}] -> [{expected_batch_size}, {expected_feature_dim}]")
                    graph_features = graph_features.unsqueeze(0).expand(expected_batch_size, -1)
                else:
                    # Unexpected 1D size: try to reshape if possible
                    if total_size % expected_feature_dim == 0:
                        inferred_batch_size = total_size // expected_feature_dim
                        print(f"   • Inferred batch size {inferred_batch_size} from total size {total_size}")
                        graph_features = graph_features.view(inferred_batch_size, expected_feature_dim)
                        if inferred_batch_size != expected_batch_size:
                            print(f"   • Batch size mismatch: adjusting from {inferred_batch_size} to {expected_batch_size}")
                            if inferred_batch_size == 1:
                                graph_features = graph_features.expand(expected_batch_size, -1)
                            else:
                                # Take mean and expand
                                graph_features = graph_features.mean(dim=0, keepdim=True).expand(expected_batch_size, -1)
                    else:
                        print(f"   • Cannot reshape 1D tensor of size {total_size} to [{expected_batch_size}, {expected_feature_dim}]")
                        # Fallback: create zero features
                        graph_features = torch.zeros(expected_batch_size, expected_feature_dim, device=graph_embedding.device)
            
            elif graph_features.dim() == 2:
                # 2D tensor - check if dimensions are correct
                current_batch, current_features = graph_features.shape
                print(f"   • 2D tensor: [{current_batch}, {current_features}]")
                
                if current_batch == expected_batch_size and current_features == expected_feature_dim:
                    # Perfect match - no changes needed
                    print(f"   • Perfect shape match: no changes needed")
                elif current_batch == 1 and current_features == expected_feature_dim:
                    # Single sample: expand to batch
                    print(f"   • Expanding single sample to batch: [1, {current_features}] -> [{expected_batch_size}, {current_features}]")
                    graph_features = graph_features.expand(expected_batch_size, -1)
                elif current_features == expected_feature_dim:
                    # Correct feature dim, wrong batch size
                    print(f"   • Batch size mismatch: adjusting from {current_batch} to {expected_batch_size}")
                    if current_batch > expected_batch_size:
                        # Take first samples
                        graph_features = graph_features[:expected_batch_size]
                    else:
                        # Take mean and expand
                        graph_features = graph_features.mean(dim=0, keepdim=True).expand(expected_batch_size, -1)
                else:
                    print(f"   • Feature dimension mismatch: expected {expected_feature_dim}, got {current_features}")
                    # Try to adjust feature dimension
                    if current_features > expected_feature_dim:
                        # Truncate features
                        graph_features = graph_features[:, :expected_feature_dim]
                        print(f"   • Truncated features to {expected_feature_dim}")
                    else:
                        # Pad with zeros
                        padding = torch.zeros(current_batch, expected_feature_dim - current_features, device=graph_embedding.device)
                        graph_features = torch.cat([graph_features, padding], dim=1)
                        print(f"   • Padded features to {expected_feature_dim}")
                    
                    # Now adjust batch size if needed
                    if graph_features.size(0) != expected_batch_size:
                        if graph_features.size(0) == 1:
                            graph_features = graph_features.expand(expected_batch_size, -1)
                        else:
                            graph_features = graph_features.mean(dim=0, keepdim=True).expand(expected_batch_size, -1)
            
            else:
                print(f"   • Unexpected graph_features dimensions: {graph_features.dim()}D")
                # Fallback: create zero features
                graph_features = torch.zeros(expected_batch_size, expected_feature_dim, device=graph_embedding.device)
            
            print(f"   • Final graph_features shape: {graph_features.shape}")
            
            # Final validation
            if graph_features.size(0) != expected_batch_size or graph_features.size(1) != expected_feature_dim:
                raise ValueError(f"Graph features final shape mismatch: expected [{expected_batch_size}, {expected_feature_dim}], got {graph_features.shape}")
            
            # Concatenate the properly shaped graph features
            graph_embedding = torch.cat([graph_embedding, graph_features], dim=1)
            print(f"   • Concatenated features shape: {graph_embedding.shape}")
            
            # Check if we need to recreate the classifier with new input dimension
            actual_input_dim = graph_embedding.shape[1]
            current_classifier_input_dim = None
            
            # Safely get current classifier input dimension
            if hasattr(self, 'classifier') and self.classifier is not None:
                if len(self.classifier) > 0 and hasattr(self.classifier[0], 'in_features'):
                    current_classifier_input_dim = self.classifier[0].in_features
            
            print(f"   • Classifier check: current_dim={current_classifier_input_dim}, actual_dim={actual_input_dim}")
            
            if current_classifier_input_dim is None or current_classifier_input_dim != actual_input_dim:
                # Need to recreate classifier with correct input dimension
                print(f"🔧 PlainGNN: Dynamically adjusting classifier input dimension from {current_classifier_input_dim} to {actual_input_dim}")
                
                # Store current device
                current_device = graph_embedding.device
                
                # Recreate classifier with correct dimension
                self._create_classifier(
                    input_dim=actual_input_dim,
                    hidden_dim=self.gnn_hidden_dim,
                    dropout_rate=self.dropout_rate,
                    use_layer_norm=self.use_layer_norm
                )
                
                # Move to same device
                self.classifier = self.classifier.to(current_device)
                
                print(f"✅ PlainGNN: Classifier dynamically adjusted to handle {actual_input_dim} dimensions")
        
        # CRITICAL FIX: Also check classifier dimension when no graph features are present
        else:
            actual_input_dim = graph_embedding.shape[1]
            current_classifier_input_dim = None
            
            # Safely get current classifier input dimension
            if hasattr(self, 'classifier') and self.classifier is not None:
                if len(self.classifier) > 0 and hasattr(self.classifier[0], 'in_features'):
                    current_classifier_input_dim = self.classifier[0].in_features
            
            print(f"   • Classifier check (no graph features): current_dim={current_classifier_input_dim}, actual_dim={actual_input_dim}")
            
            if current_classifier_input_dim is None or current_classifier_input_dim != actual_input_dim:
                # Need to recreate classifier with correct input dimension
                print(f"🔧 PlainGNN: Adjusting classifier for pooled embedding dimension from {current_classifier_input_dim} to {actual_input_dim}")
                
                # Store current device
                current_device = graph_embedding.device
                
                # Recreate classifier with correct dimension
                self._create_classifier(
                    input_dim=actual_input_dim,
                    hidden_dim=self.gnn_hidden_dim,
                    dropout_rate=self.dropout_rate,
                    use_layer_norm=self.use_layer_norm
                )
                
                # Move to same device
                self.classifier = self.classifier.to(current_device)
                
                print(f"✅ PlainGNN: Classifier adjusted for pooled embedding dimension {actual_input_dim}")
        
        # Output layer with enhanced validation and diversity mechanisms
        output = self.classifier(graph_embedding)
        
        # Validate output for NaN values
        if torch.isnan(output).any():
            print(f"⚠️ Critical: NaN values in PlainGNN final output")
            output = torch.nan_to_num(output, nan=0.0)
        
        # Enhanced prediction diversity for PlainGNN
        output_std = safe_item(output.std())
        # output_range = safe_item(output.max() - output.min())  # Not currently used
        
        # SIMPLIFIED: Apply basic safety measures only, rely on diversity loss for training
        # Basic numerical safety - clamp to prevent extreme values
        output = torch.clamp(output, min=config.OUTPUT_CLAMP_MIN, max=config.OUTPUT_CLAMP_MAX)
        
        # Optional: Light temperature scaling only for extremely concentrated predictions
        if config.AUTO_TEMPERATURE_SCALING and output_std < 0.01:  # Only for very extreme cases
            if config.VERBOSE_DIVERSITY_LOGGING:
                print(f"   ⚠️ PlainGNN: Applying safety temperature scaling for extreme concentration (std={output_std:.4f})")
            output = output / 1.5  # Light scaling
        
        # CRITICAL FIX: Ensure output shape matches target shape
        # Targets are typically [batch_size] but model outputs [batch_size, 1]
        # Squeeze the last dimension to match target shape
        if output.dim() == 2 and output.size(1) == 1:
            output = output.squeeze(1)  # [batch_size, 1] -> [batch_size]
            # Only log shape adjustment in verbose mode as this is normal behavior
            if config.VERBOSE_DIVERSITY_LOGGING:
                print(f"   • PlainGNN: Squeezed output to match target shape: {output.shape}")
            
            # CRITICAL FIX: Ensure squeeze didn't create a scalar
            if output.dim() == 0:
                print(f"❌ Critical Error: squeeze operation created a scalar! Reshaping to [1]")
                output = output.unsqueeze(0)  # scalar -> [1]
            
            # Double-check tensor integrity after squeeze
            if not isinstance(output, torch.Tensor):
                print(f"❌ Critical Error: output became {type(output)} after squeeze!")
                raise TypeError(f"Squeeze operation converted tensor to {type(output)}")
        
        return output


# Model factory function for easy instantiation
def create_model(model_type: str, **kwargs) -> nn.Module:
    """
    Factory function to create models based on type.
    
    Args:
        model_type: Type of model to create ('tsap_gnn_lstm' or 'plain_gnn')
        **kwargs: Model-specific parameters
        
    Returns:
        Instantiated model
        
    Raises:
        ValueError: If model type is not supported
    """
    # Apply configuration defaults
    enhanced_kwargs = {
        'use_residual': getattr(config, 'USE_RESIDUAL_CONNECTIONS', True),
        'use_layer_norm': getattr(config, 'USE_LAYER_NORMALIZATION', True),
        'dropout_rate': kwargs.get('dropout_rate', 0.2),
        **kwargs
    }

    if model_type == 'tsap_gnn_lstm':
        model = TSAPGnnLSTM(**enhanced_kwargs)
        # CRITICAL FIX: Initialize weights for better prediction diversity
        model._initialize_weights()
        return model
    elif model_type == 'plain_gnn':
        model = PlainGNN(**enhanced_kwargs)
        # CRITICAL FIX: Initialize weights for better prediction diversity
        model._initialize_weights()
        return model
    else:
        raise ValueError(f"Unknown model type: {model_type}. Supported: {config.MODEL_TYPES}")


# Backward compatibility alias
TSAPGnnLSTM_Legacy = TSAPGnnLSTM