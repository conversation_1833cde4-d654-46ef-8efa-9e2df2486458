# GNN-LSTM Financial Crisis Prediction System - AI Coding Instructions

## Architecture Overview

This is a **financial crisis prediction system** using Graph Neural Networks (GNN) with LSTM for temporal modeling. The core innovation is **TSAP (Temporal Spectral Attention Pooling)** architecture with comprehensive data leakage prevention.

### Key Components & Data Flow
```
Raw CSV → Graph Builder → TSAP/PlainGNN Models → F0.5 Optimization → Temporal CV
```

1. **Financial Graph Builder** (`gnn_lstm/financial_graph_builder/`): Builds dynamic financial correlation graphs using multi-metric correlation (<PERSON>, <PERSON>, <PERSON><PERSON>)
2. **TSAP Architecture** (`gnn_lstm/models.py`): Primary model with spectral clustering and attention pooling
3. **Temporal Cross-Validation** (`gnn_lstm/data_utils.py`): Strict chronological splits with 3-month embargo to prevent data leakage
4. **F0.5 Optimization** (`gnn_lstm/evaluation.py`): Precision-weighted metric optimization for crisis prediction

## Critical Conventions

### Configuration Management
- **ALL parameters live in `gnn_lstm/config.py`** (200+ centralized parameters)
- Use `getattr(config, 'PARAM_NAME', default_value)` pattern for safe access
- Device auto-detection: `config.DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")`

### Error Handling Policy
**CRITICAL**: This codebase has **zero error suppression** by design. All try/except blocks removed per user policy.
- Errors surface naturally and halt execution immediately
- No silent failures, warnings filtering, or error masking
- This ensures full transparency for financial model validation

### Data Leakage Prevention
- **Temporal ordering is sacred**: Always use `TimeSeriesSplit` with embargo periods
- **3-month embargo**: 90-sample purge periods around test sets
- **Label shifting**: 12-month advance prediction (crisis signals come before events)
- Validation checks in `data_utils.py` detect leakage across multiple dimensions

### Model Architecture Patterns
```python
# TSAP models use tuple outputs for auxiliary loss
if isinstance(outputs, tuple):
    main_outputs, aux_outputs = outputs
    # Handle auxiliary loss computation

# Enhanced loss with diversity penalty
total_loss, main_loss, aux_loss = calculate_enhanced_loss(outputs, targets, criterion)
```

## Essential Workflows

### Training Pipeline
```bash
python main.py  # Entry point with system validation
python pipeline.py  # Core training with temporal CV
```

### Hyperparameter Optimization
- Uses **Optuna** with 50 trials, 3-hour timeout
- Multi-objective: F0.5 score + training-validation gap penalty
- Search space includes graph parameters (window size, top-k edges) and architecture (hidden dims, attention heads)

### Performance Targets
- **F0.5 Score**: 0.72-0.78 (precision-weighted for crisis prediction)
- **Train-Val Gap**: <0.05 F0.5 points maximum
- **Convergence**: 100-300 epochs with 35-epoch patience

## Integration Points

### Graph Construction
- **Blockwise correlation**: Single optimized computation for all samples
- **Rank aggregation**: Combines multiple correlation metrics using weighted ranks
- **Dynamic edges**: Top-K selection based on correlation strength
- Memory-efficient with explicit garbage collection

### TSAP Clustering
- **K-means spectral clustering** (K=5 clusters) on graph Laplacian
- **Super-adjacency matrix** construction with τ=0.5 threshold
- **LSTM temporal encoding** (hidden_dim=64) before attention pooling

### Model Variants
- **TSAPGnnLSTM**: Primary model (60% weight in ensemble)
- **PlainGNN**: Baseline without TSAP (40% weight)
- Both support GAT, GraphConv, and TransformerConv layers

## Development Patterns

### File Editing
- When modifying models, always update both TSAP and PlainGNN variants
- Graph feature dimensions must be exactly 16 (validated throughout pipeline)
- Use `safe_item()` helper for tensor→scalar conversion

### Testing & Validation
- Run `validate_tsap_enhanced.py` after model changes
- Use `comprehensive_results_report.py` for full pipeline evaluation
- Check temporal ordering with `data_utils.validate_temporal_splits()`

### Dependencies
- PyTorch 1.12-2.1, PyTorch Geometric 2.3-2.5
- Optuna 3.1-3.5 for hyperparameter optimization
- Windows compatible (PowerShell commands, thread-safe operations)
