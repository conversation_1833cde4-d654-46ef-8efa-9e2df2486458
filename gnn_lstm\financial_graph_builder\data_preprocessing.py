#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced data preprocessing module for GNN-LSTM financial crisis prediction.
Provides comprehensive data cleaning, feature engineering, and validation.
"""

from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import scipy.sparse
from sklearn.experimental import enable_iterative_imputer  # noqa: F401
from sklearn.impute import IterativeImputer
from sklearn.linear_model import Ridge
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

from .. import config

def safe_drop_features(df: pd.DataFrame, features_to_drop: List[str], step_name: str = "unknown") -> pd.DataFrame:
    """
    Safely drop features from dataframe, only attempting to drop features that actually exist.
    
    This function prevents KeyError when trying to drop features that don't exist in the current
    dataframe, which can happen when features have been dropped in previous preprocessing steps.
    
    Args:
        df: DataFrame to drop features from
        features_to_drop: List of feature names to drop
        step_name: Name of the preprocessing step for logging
        
    Returns:
        DataFrame with specified features dropped (only if they existed)
    """
    if not features_to_drop:
        return df
    
    # Find which features actually exist in the current dataframe
    existing_features = [col for col in features_to_drop if col in df.columns]
    non_existing_features = [col for col in features_to_drop if col not in df.columns]
    
    # Log information about feature dropping
    if existing_features:
        print(f"     - {step_name}: Dropping {len(existing_features)} existing features")
        df = df.drop(columns=existing_features)
    
    if non_existing_features:
        print(f"     - {step_name}: Skipping {len(non_existing_features)} features (already dropped): {non_existing_features[:5]}{'...' if len(non_existing_features) > 5 else ''}")
    
    return df

def validate_feature_existence(df: pd.DataFrame, required_features: List[str], step_name: str = "unknown") -> None:
    """
    Validate that required features exist in the dataframe.
    
    Args:
        df: DataFrame to check
        required_features: List of features that should exist
        step_name: Name of the preprocessing step for error messages
        
    Raises:
        ValueError: If required features are missing
    """
    missing_features = [col for col in required_features if col not in df.columns]
    
    if missing_features:
        raise ValueError(
            f"{step_name}: Required features missing from dataframe: {missing_features[:10]}... "
            f"({len(missing_features)} total missing)"
        )

def track_feature_changes(df_before: pd.DataFrame, df_after: pd.DataFrame, step_name: str) -> None:
    """
    Track and log feature changes between preprocessing steps.
    
    Args:
        df_before: DataFrame before the step
        df_after: DataFrame after the step
        step_name: Name of the preprocessing step
    """
    features_before = set(df_before.columns)
    features_after = set(df_after.columns)
    
    dropped_features = features_before - features_after
    added_features = features_after - features_before
    
    print(f"     - {step_name}: {len(df_before.columns)} → {len(df_after.columns)} features")
    if dropped_features:
        print(f"       * Dropped: {len(dropped_features)} features")
    if added_features:
        print(f"       * Added: {len(added_features)} features")

def load_raw_data(csv_path: str) -> pd.DataFrame:
    """
    Load raw financial data from CSV file.
    """
    print(f"[Data Loading] Loading raw CSV: {csv_path}")
    df = pd.read_csv(csv_path, encoding="utf-8")
    print(f"✅ Loaded data with shape: {df.shape}")
    
    if config.RESPONSE_COL not in df.columns:
        raise ValueError(f"Required column '{config.RESPONSE_COL}' not found in dataset")
    
    return df

def apply_label_shifting(df: pd.DataFrame, shift_months: Optional[int] = None) -> pd.DataFrame:
    """
    Apply proper label shifting for advance prediction.
    
    Shifts the response variable forward by the specified number of months
    to enable prediction of future financial crises. This is critical for
    preventing data leakage in temporal forecasting.
    
    Args:
        df: DataFrame with time series data
        shift_months: Number of months to shift labels forward (default from config)
        
    Returns:
        DataFrame with properly shifted labels, truncated to remove NaN labels
    """
    # Ensure shift_months is always an integer
    if shift_months is None:
        shift_months = getattr(config, 'LABEL_SHIFT_MONTHS', 12)
    
    # Validate shift_months is a positive integer
    if not isinstance(shift_months, int) or shift_months <= 0:
        raise ValueError(f"shift_months must be a positive integer, got {shift_months}")
    
    print(f"🔄 Applying {shift_months}-month label shifting for advance prediction...")
    
    df_shifted = df.copy()
    
    # Shift the response column forward by shift_months
    # This means we're predicting the crisis state shift_months into the future
    df_shifted[config.RESPONSE_COL] = df_shifted[config.RESPONSE_COL].shift(-shift_months)
    
    # Remove rows where labels are NaN due to shifting
    # These are the last shift_months rows that don't have future labels
    original_length = len(df_shifted)
    df_shifted = df_shifted.dropna(subset=[config.RESPONSE_COL])
    final_length = len(df_shifted)
    
    removed_samples = original_length - final_length
    print(f"✅ Label shifting complete:")
    print(f"   • Shifted labels {shift_months} months forward")
    print(f"   • Removed {removed_samples} samples without future labels")
    print(f"   • Final dataset size: {final_length} samples")
    
    # Validate that we still have enough data
    if final_length < 100:  # Minimum reasonable dataset size
        raise ValueError(f"After label shifting, only {final_length} samples remain. This may be insufficient for training.")
    
    # Check label distribution after shifting
    labels = df_shifted[config.RESPONSE_COL].values
    # Convert to numpy array first, then calculate mean
    labels_array = np.asarray(labels, dtype=np.float64)
    positive_ratio = float(labels_array.mean())
    print(f"   • Label distribution after shifting: {positive_ratio:.3f} positive ratio")
    
    return df_shifted

def extract_labels_and_features(df: pd.DataFrame) -> tuple:
    """
    Extract labels and feature data from DataFrame.
    """
    if config.TIME_COL in df.columns:
        df = df.drop(columns=[config.TIME_COL])

    labels = df[config.RESPONSE_COL].values
    df = df.drop(columns=[config.RESPONSE_COL])
    
    df_num = df.select_dtypes(include=[np.number]).copy()
    feature_names = list(df_num.columns)
    
    print(f"✅ Extracted {len(feature_names)} numeric features and {len(labels)} labels")
    return df_num, labels, feature_names

def handle_infinite_values(df: pd.DataFrame) -> pd.DataFrame:
    """
    Cap infinite values to column finite extrema.
    """
    df_clean = df.copy()
    for col in df_clean.columns:
        col_vals = df_clean[col]
        finite = col_vals[np.isfinite(col_vals)]
        if finite.size > 0:
            vmin, vmax = finite.min(), finite.max()
            df_clean[col].replace(np.inf, vmax, inplace=True)
            df_clean[col].replace(-np.inf, vmin, inplace=True)
    return df_clean

def filter_high_na_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Remove features with high missing value ratios.
    """
    na_frac = df.isna().mean(axis=0)
    drop_cols = na_frac[na_frac > config.MAX_NA_RATIO].index.tolist()
    if drop_cols:
        print(f"🗑️  Dropping {len(drop_cols)} features with high NA ratio")
        df = df.drop(columns=drop_cols)
    return df

def winsorize_data(df: pd.DataFrame, lower_quantile: float = 0.01, upper_quantile: float = 0.99) -> pd.DataFrame:
    """
    Winsorize data to specified quantiles.
    """
    lower = df.quantile(lower_quantile, numeric_only=True)
    upper = df.quantile(upper_quantile, numeric_only=True)
    return df.clip(lower=lower, upper=upper, axis=1)

def impute_missing_values(df: pd.DataFrame, method: str) -> pd.DataFrame:
    """
    Impute missing values using specified method.
    """
    if method == 'mice':
        # Apply enhanced pre-imputation strategy
        df_copy = df.copy()
        pre_impute_threshold = getattr(config, 'PRE_IMPUTE_THRESHOLD', 0.3)
        pre_impute_method = getattr(config, 'PRE_IMPUTE_METHOD', 'median')
        
        high_na_cols = df_copy.columns[df_copy.isna().mean() > pre_impute_threshold].tolist()
        if high_na_cols:
            for col in high_na_cols:
                if pre_impute_method == 'median':
                    fill_val = df_copy[col].median()
                elif pre_impute_method == 'mean':
                    fill_val = df_copy[col].mean()
                else:
                    fill_val = df_copy[col].median()  # Default to median
                
                # Handle case where fill_val might be NaN (all values missing)
                if pd.isna(fill_val):
                    fill_val = 0.0
                
                df_copy[col] = df_copy[col].fillna(fill_val)
        
        # Enhanced MICE configuration
        mice_tol = getattr(config, 'MICE_TOL', 1e-3)
        mice_verbose = getattr(config, 'MICE_VERBOSE', 0)
        imputer = IterativeImputer(
            estimator=Ridge(alpha=config.MICE_RIDGE_ALPHA), 
            random_state=config.MICE_RANDOM_STATE, 
            max_iter=config.MICE_MAX_ITER,
            tol=mice_tol,
            verbose=mice_verbose
        )
        arr_imp = imputer.fit_transform(df_copy)
        # Ensure we have a proper numpy array for DataFrame construction
        # Check if result is a scipy sparse matrix and convert to dense array
        if scipy.sparse.issparse(arr_imp):
            # Handle scipy sparse matrix by converting to dense array
            arr_imp = arr_imp.toarray()  # type: ignore
        arr_imp = np.asarray(arr_imp, dtype=np.float32)
        return pd.DataFrame(arr_imp, columns=df_copy.columns, index=df_copy.index)
    elif method == 'mean' or method == 'median':
        from sklearn.impute import SimpleImputer
        imputer = SimpleImputer(strategy=method)
        arr_imp = imputer.fit_transform(df)
        # Ensure we have a proper numpy array for DataFrame construction
        # Check if result is a scipy sparse matrix and convert to dense array
        if scipy.sparse.issparse(arr_imp):
            # Handle scipy sparse matrix by converting to dense array
            arr_imp = arr_imp.toarray()  # type: ignore
        arr_imp = np.asarray(arr_imp, dtype=np.float32)
        return pd.DataFrame(arr_imp, columns=df.columns, index=df.index)
    elif method == 'ffill_bfill':
        df.replace([np.inf, -np.inf], np.nan, inplace=True)
        df_imputed = df.ffill().bfill()
        return df_imputed.fillna(0)
    else:
        raise ValueError(f"Unknown imputation method: {method}")

def filter_constant_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Remove near-constant features based on standard deviation.
    Enhanced to prevent correlation computation warnings by using more aggressive thresholds.
    """
    # Use more aggressive threshold to prevent correlation computation issues
    # Correlation computation fails when variance < 1e-12, so we use 1e-10 as safety margin
    aggressive_threshold = max(config.CONSTANT_FEATURE_THRESHOLD, 1e-10)
    
    stds = df.std(axis=0)
    keep = stds >= aggressive_threshold
    
    if (~keep).any():
        dropped_features = df.columns[~keep].tolist()
        print(f"🗑️  Dropping {np.sum(~keep)} near-constant features (std < {aggressive_threshold:.2e})")
        print(f"     Features dropped: {dropped_features[:5]}{'...' if len(dropped_features) > 5 else ''}")
        df = df.loc[:, keep]
    
    return df

def ensure_data_quality_for_imputation(df: pd.DataFrame) -> pd.DataFrame:
    """
    Ensure data quality before imputation to prevent convergence issues.
    
    This function handles edge cases that can prevent MICE from converging:
    - Features with extreme outliers
    - Features with very high correlation (multicollinearity)
    - Features with unusual distributions
    Enhanced with comprehensive validation to prevent NaN propagation.
    """
    print("🔍 Ensuring data quality for imputation...")
    df_clean = df.copy()
    
    # Comprehensive data validation first
    initial_shape = df_clean.shape
    print(f"   • Initial shape: {initial_shape}")
    
    # Check for any remaining non-numeric data
    non_numeric_cols = []
    for col in df_clean.columns:
        if not pd.api.types.is_numeric_dtype(df_clean[col]):
            non_numeric_cols.append(col)
            # Force conversion to numeric, replacing non-convertible with NaN
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
    
    if non_numeric_cols:
        print(f"   • Converted {len(non_numeric_cols)} non-numeric columns to numeric")
    
    # Handle infinite values first (before outlier detection)
    inf_counts = {}
    for col in df_clean.columns:
        if df_clean[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            inf_mask = np.isinf(df_clean[col])
            if inf_mask.any():
                inf_count = inf_mask.sum()
                inf_counts[col] = inf_count
                
                # Replace infinite values with NaN, then immediately impute NaNs with column median
                df_clean[col].replace([np.inf, -np.inf], np.nan, inplace=True)
                # Impute NaNs with column median (if all values are NaN, fill with 0)
                median_val = df_clean[col].median()
                if np.isnan(median_val):
                    median_val = 0.0
                df_clean[col].fillna(median_val, inplace=True)
    
    if inf_counts:
        total_infs = sum(inf_counts.values())
        print(f"   • Replaced {total_infs} infinite values with NaN across {len(inf_counts)} columns")
    
    # Handle extreme outliers that can prevent convergence
    outlier_counts = {}
    for col in df_clean.columns:
        if df_clean[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            # Only process columns with sufficient finite data
            finite_data = df_clean[col].dropna()
            if len(finite_data) < 10:  # Need minimum data for outlier detection
                continue
                
            # Use IQR method to cap extreme outliers
            Q1 = finite_data.quantile(0.25)
            Q3 = finite_data.quantile(0.75)
            IQR = Q3 - Q1
            
            if IQR > 1e-12:  # Only apply if there's sufficient variation
                lower_bound = Q1 - 3 * IQR  # More conservative than typical 1.5 * IQR
                upper_bound = Q3 + 3 * IQR
                
                # Count outliers before capping
                outlier_mask = (df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)
                outliers = outlier_mask.sum()
                if outliers > 0:
                    outlier_counts[col] = outliers
                    df_clean[col] = df_clean[col].clip(lower=lower_bound, upper=upper_bound)
    
    if outlier_counts:
        total_outliers = sum(outlier_counts.values())
        print(f"   • Capped {total_outliers} extreme outliers across {len(outlier_counts)} columns")
    
    # Final validation: ensure no infinite or extremely large values remain
    validation_issues = {}
    for col in df_clean.columns:
        if df_clean[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            finite_data = df_clean[col].dropna()
            if len(finite_data) > 0:
                # Check for remaining extreme values
                abs_max = np.abs(finite_data).max()
                if abs_max > 1e10:  # Extremely large values
                    validation_issues[col] = f"max_value={abs_max:.2e}"
                    # Cap to reasonable range
                    df_clean[col] = df_clean[col].clip(lower=-1e10, upper=1e10)
    
    if validation_issues:
        print(f"   • Capped extremely large values in {len(validation_issues)} columns")
    
    # Check for columns with no variation (all same value)
    constant_cols = []
    for col in df_clean.columns:
        finite_data = df_clean[col].dropna()
        if len(finite_data) > 1:
            if finite_data.std() < 1e-12:
                constant_cols.append(col)
    
    if constant_cols:
        print(f"   • Found {len(constant_cols)} near-constant columns")
    
    # Final shape check
    final_shape = df_clean.shape
    print(f"   • Final shape: {final_shape}")
    print("✅ Data quality ensured for imputation")
    
    return df_clean

def fit_preprocessors(train_df: pd.DataFrame) -> Dict[str, Any]:
    """
    Fit all necessary preprocessors on the training data with strict data leakage prevention.
    
    This function enforces the strict fit-transform pattern where all preprocessing
    parameters are learned ONLY from the training data and then applied to 
    validation and test sets to prevent data leakage.
    """
    print("=" * 60, "\n🛠️  FITTING PREPROCESSORS ON TRAINING DATA (STRICT MODE)", "\n" + "=" * 60)
    
    # Validate strict normalization is enabled
    if not getattr(config, 'STRICT_NORMALIZATION', True):
        raise ValueError("STRICT_NORMALIZATION must be True to prevent data leakage")
    
    # Extract features and labels first to get feature_names
    train_features, _, feature_names = extract_labels_and_features(train_df)
    # Initialize preprocessing state manager and feature manager
    state_manager = PreprocessingStateManager()
    feature_manager = RobustFeatureManager(feature_names)
    validation_history = []  # Track validation results throughout preprocessing

    preprocessors = {}
    preprocessors['strict_mode'] = True  # Flag to indicate strict preprocessing
    preprocessors['state_manager'] = state_manager
    preprocessors['feature_manager'] = feature_manager
    
    print("🔍 Validating training data for preprocessing...")
    if len(train_df) == 0:
        raise ValueError("Training DataFrame is empty")

    # Check if labels have been properly shifted
    if config.RESPONSE_COL in train_df.columns:
        labels = np.asarray(train_df[config.RESPONSE_COL].values, dtype=np.float64)
        if np.any(np.isnan(labels)):
            pass  # Do not print warning

    preprocessors['initial_feature_names'] = feature_names
    print(f"✅ Initial features: {len(feature_names)} columns")
    
    # Record initial state
    state_manager.record_step('initial', [], feature_names)
    
    # Comprehensive validation - Initial state
    initial_validation = comprehensive_data_validation(train_features, "initial_data")
    validation_history.append(initial_validation)
    log_validation_summary(initial_validation)
    
    # Step 1: Handle infinite values (no fitting required)
    print("🔧 Step 1: Handling infinite values...")
    features_before_infinite = list(train_features.columns)
    train_features = handle_infinite_values(train_features)

    # --- FIX: Ensure all columns are numeric and immediately impute any remaining NaNs with column median (or 0 if all NaN) ---
    for col in train_features.columns:
        train_features.loc[:, col] = pd.to_numeric(train_features[col], errors='coerce')
        if train_features[col].isna().any():
            median_val = train_features[col].median()
            if pd.isna(median_val):
                median_val = 0.0
            train_features[col] = train_features[col].fillna(median_val)
    # --- END FIX ---
    # --- VALIDATION: Assert no NaNs remain after infinite value handling ---
    if train_features.isna().any().any():
        raise ValueError("NaNs remain in features after infinite value handling and imputation in fit_preprocessors.")
    # --- END VALIDATION ---
    
    # Record infinite handling state
    features_after_infinite = list(train_features.columns)
    state_manager.record_step('infinite_handling', features_before_infinite, features_after_infinite)
    
    # Comprehensive validation - After infinite value handling
    infinite_validation = comprehensive_data_validation(train_features, "infinite_handling")
    validation_history.append(infinite_validation)
    log_validation_summary(infinite_validation)
    
    # Step 2: Feature filtering based on missing values (fit on training only)
    print("🔧 Step 2: Filtering high-NA features...")
    na_frac = train_features.isna().mean(axis=0)
    drop_cols_na = na_frac[na_frac > config.MAX_NA_RATIO].index.tolist()
    preprocessors['drop_cols_na'] = drop_cols_na
    preprocessors['na_thresholds'] = na_frac.to_dict()  # Store for validation
    
    if drop_cols_na:
        print(f"   • Dropping {len(drop_cols_na)} features with NA ratio > {config.MAX_NA_RATIO}")
        features_before_na_drop = list(train_features.columns)
        train_features_before = train_features.copy()
        train_features = safe_drop_features(train_features, drop_cols_na, "high-NA features (training)")
        track_feature_changes(train_features_before, train_features, "high-NA feature removal (training)")
        feature_names = list(train_features.columns)
        
        # Record with both state manager and feature manager
        state_manager.record_step('na_drop', features_before_na_drop, feature_names, 
                                {'drop_cols_na': drop_cols_na, 'max_na_ratio': config.MAX_NA_RATIO})
        feature_manager.register_step('na_drop', features_before_na_drop, feature_names, 
                                    f"high NA ratio > {config.MAX_NA_RATIO}")
    else:
        # No features dropped, update feature_names
        feature_names = list(train_features.columns)
        
        # Record no-change state
        state_manager.record_step('na_drop', list(train_features.columns), list(train_features.columns), 
                                {'drop_cols_na': [], 'max_na_ratio': config.MAX_NA_RATIO})
        feature_manager.register_step('na_drop', list(train_features.columns), list(train_features.columns), 
                                    "no high-NA features found")
    
    # Step 3: Winsorization parameters (fit on training only)
    print("🔧 Step 3: Computing winsorization parameters...")
    features_before_winsorize = list(train_features.columns)
    lower_quantile = train_features.quantile(config.WINSORIZE_LOWER_QUANTILE, numeric_only=True)
    upper_quantile = train_features.quantile(config.WINSORIZE_UPPER_QUANTILE, numeric_only=True)
    preprocessors['winsorize_quantiles'] = (lower_quantile, upper_quantile)
    print(f"   • Winsorization bounds computed from training data only")
    
    train_features = train_features.clip(lower=lower_quantile, upper=upper_quantile, axis=1)
    features_after_winsorize = list(train_features.columns)
    
    # Record winsorization state
    state_manager.record_step('winsorization', features_before_winsorize, features_after_winsorize,
                            {'lower_quantile': config.WINSORIZE_LOWER_QUANTILE, 
                             'upper_quantile': config.WINSORIZE_UPPER_QUANTILE})
    
    # Step 3.5: Ensure data quality for imputation
    print("🔧 Step 3.5: Ensuring data quality for imputation...")
    features_before_quality = list(train_features.columns)
    train_features = ensure_data_quality_for_imputation(train_features)
    features_after_quality = list(train_features.columns)
    print("   • Data quality checks completed")
    
    # Record quality check state
    state_manager.record_step('quality_check', features_before_quality, features_after_quality)

    # Step 4: Imputation (fit on training only)
    print("🔧 Step 4: Fitting imputation model...")
    features_before_imputation = list(train_features.columns)
    imputer = None
    if config.IMPUTATION_METHOD == 'mice':
        # --- Enhanced pre-imputation strategy to improve MICE convergence ---
        pre_impute_threshold = getattr(config, 'PRE_IMPUTE_THRESHOLD', 0.3)
        pre_impute_method = getattr(config, 'PRE_IMPUTE_METHOD', 'median')
        
        high_na_cols = train_features.columns[train_features.isna().mean() > pre_impute_threshold].tolist()
        if high_na_cols:
            print(f"   • {len(high_na_cols)} features have >{pre_impute_threshold*100:.0f}% missing values. Pre-imputing with {pre_impute_method} before MICE...")
            for col in high_na_cols:
                if pre_impute_method == 'median':
                    fill_val = train_features[col].median()
                elif pre_impute_method == 'mean':
                    fill_val = train_features[col].mean()
                else:
                    fill_val = train_features[col].median()  # Default to median
                
                # Handle case where fill_val might be NaN (all values missing)
                if pd.isna(fill_val):
                    fill_val = 0.0
                
                train_features[col] = train_features[col].fillna(fill_val)
        else:
            print(f"   • No features with >{pre_impute_threshold*100:.0f}% missing values before MICE.")
        
        # Enhanced MICE configuration for better convergence
        mice_tol = getattr(config, 'MICE_TOL', 1e-3)
        mice_verbose = getattr(config, 'MICE_VERBOSE', 0)
        imputer = IterativeImputer(
            estimator=Ridge(alpha=config.MICE_RIDGE_ALPHA), 
            random_state=config.MICE_RANDOM_STATE, 
            max_iter=config.MICE_MAX_ITER,
            tol=mice_tol,
            verbose=mice_verbose
        )
        print(f"   • Fitting MICE imputer on training data only (max_iter={config.MICE_MAX_ITER}, tol={mice_tol})...")
        imputer.fit(train_features)
        print("   • MICE imputer fitted successfully")
    elif config.IMPUTATION_METHOD in ['mean', 'median']:
        from sklearn.impute import SimpleImputer
        imputer = SimpleImputer(strategy=config.IMPUTATION_METHOD)
        print(f"   • Fitting {config.IMPUTATION_METHOD} imputer on training data only...")
        imputer.fit(train_features)
        print(f"   • {config.IMPUTATION_METHOD} imputer fitted successfully")
    preprocessors['imputer'] = imputer
    preprocessors['imputation_method'] = config.IMPUTATION_METHOD
    # Apply imputation
    if imputer:
        train_features_imp = imputer.transform(train_features)
        # Ensure we have a proper numpy array for DataFrame construction
        # Check if result is a scipy sparse matrix and convert to dense array
        if scipy.sparse.issparse(train_features_imp):
            # Handle scipy sparse matrix by converting to dense array
            train_features_imp = train_features_imp.toarray()  # type: ignore
        train_features_imp = np.asarray(train_features_imp, dtype=np.float32)
        
        train_features = pd.DataFrame(
            data=train_features_imp,
            columns=train_features.columns,
            index=train_features.index
        )
        # No print for missing values
    else:
        train_features = train_features.ffill().bfill().fillna(0)
    
    # Record imputation state
    features_after_imputation = list(train_features.columns)
    state_manager.record_step('imputation', features_before_imputation, features_after_imputation,
                            {'method': config.IMPUTATION_METHOD})

    # Comprehensive validation - After imputation
    imputation_validation = comprehensive_data_validation(train_features, "imputation")
    validation_history.append(imputation_validation)
    log_validation_summary(imputation_validation)

    # Step 5: Constant feature filtering (fit on training only)
    print("🔧 Step 5: Filtering constant features...")
    features_before_constant = list(train_features.columns)
    stds = train_features.std(axis=0)
    
    # Use aggressive threshold to prevent correlation computation issues
    # Correlation computation fails when variance < 1e-12, so we use 1e-10 as safety margin
    aggressive_threshold = max(config.CONSTANT_FEATURE_THRESHOLD, 1e-10)
    
    keep_cols_std = stds >= aggressive_threshold
    preprocessors['keep_cols_std'] = keep_cols_std
    preprocessors['training_stds'] = stds.to_dict()  # Store for validation
    preprocessors['constant_threshold_used'] = aggressive_threshold  # Store the actual threshold used
    
    if (~keep_cols_std).any():
        dropped_constant = np.sum(~keep_cols_std)
        dropped_features = train_features.columns[~keep_cols_std].tolist()
        print(f"   • Dropping {dropped_constant} near-constant features (std < {aggressive_threshold:.2e})")
        print(f"     Features dropped: {dropped_features[:5]}{'...' if len(dropped_features) > 5 else ''}")
        train_features_before_const = train_features.copy()
        train_features = train_features.loc[:, keep_cols_std]
        track_feature_changes(train_features_before_const, train_features, "constant feature removal (training)")
        feature_names = list(train_features.columns)
    else:
        feature_names = list(train_features.columns)
    
    # Record constant filtering state with both managers
    features_after_constant = list(train_features.columns)
    state_manager.record_step('constant_filter', features_before_constant, features_after_constant,
                            {'threshold': aggressive_threshold, 'dropped_count': np.sum(~keep_cols_std)})
    feature_manager.register_step('constant_filter', features_before_constant, features_after_constant,
                                f"low variance < {aggressive_threshold:.2e}")

    # Step 6: Feature selection (placeholder - fit on training only)
    features_before_selection = list(train_features.columns)
    preprocessors['selected_features'] = feature_names  # No additional feature selection for now
    print(f"   • Selected features: {len(feature_names)} columns")
    
    # Record selection state
    features_after_selection = feature_names
    state_manager.record_step('selection', features_before_selection, features_after_selection,
                            {'selection_method': 'all'})

    # Step 7: Standardization (CRITICAL - fit on training only) - Enhanced for numerical stability
    print("🔧 Step 7: Fitting robust standardization scaler...")
    scaler = StandardScaler()
    print("   • Computing mean and std from training data only...")
    
    # Enhanced pre-scaling validation to prevent numerical issues
    print("   • Performing pre-scaling validation...")
    features_for_scaling = train_features.values.astype(np.float32)
    
    # Check for extreme values that could cause scaling issues
    for i, col_name in enumerate(train_features.columns):
        col_data = features_for_scaling[:, i]
        finite_data = col_data[np.isfinite(col_data)]
        
        if len(finite_data) == 0:
            print(f"     ⚠️ Warning: Column {col_name} has no finite values, filling with 0")
            features_for_scaling[:, i] = 0.0
            continue
            
        # Check for extremely small standard deviation that could cause numerical issues
        col_std = np.std(finite_data)
        if col_std < 1e-8:
            print(f"     ⚠️ Warning: Column {col_name} has very small std ({col_std:.2e}), adding small noise for numerical stability")
            # Add tiny amount of noise to prevent division by zero in standardization
            noise = np.random.normal(0, 1e-7, size=features_for_scaling[:, i].shape)
            features_for_scaling[:, i] = features_for_scaling[:, i] + noise
        
        # Check for extreme outliers that could skew scaling
        col_mean = np.mean(finite_data)
        if col_std > 0:
            # Cap extreme outliers to ±5 standard deviations to prevent scaling issues
            lower_bound = col_mean - 5 * col_std
            upper_bound = col_mean + 5 * col_std
            extreme_outliers = np.sum((finite_data < lower_bound) | (finite_data > upper_bound))
            if extreme_outliers > 0:
                print(f"     📊 Column {col_name}: Capped {extreme_outliers} extreme outliers for stable scaling")
                features_for_scaling[:, i] = np.clip(features_for_scaling[:, i], lower_bound, upper_bound)
    
    # Update train_features with the cleaned data
    train_features = pd.DataFrame(
        data=features_for_scaling,
        columns=train_features.columns,
        index=train_features.index
    )
    
    # Fit the scaler with enhanced validation
    scaler.fit(features_for_scaling)
    
    # Validate scaler was fitted properly and check for numerical issues
    if not hasattr(scaler, 'mean_') or not hasattr(scaler, 'scale_'):
        raise RuntimeError("StandardScaler failed to fit properly")
    
    # Enhanced scaler validation for numerical stability
    scaler_mean = scaler.mean_
    scaler_scale = scaler.scale_
    
    # Check for problematic scaling factors
    # CRITICAL FIX: Check for None before operations to prevent Pylance errors
    if scaler_scale is not None:
        small_scale_indices = np.where(scaler_scale < 1e-6)[0]
        large_scale_indices = np.where(scaler_scale > 1e6)[0]
        
        if len(small_scale_indices) > 0:
            print(f"     ⚠️ Warning: {len(small_scale_indices)} features have very small scale factors, adjusting for stability")
            for idx in small_scale_indices:
                if hasattr(scaler, 'scale_') and scaler.scale_ is not None:
                    scaler.scale_[idx] = 1e-6  # Set minimum scale factor
        
        if len(large_scale_indices) > 0:
            print(f"     ⚠️ Warning: {len(large_scale_indices)} features have very large scale factors, adjusting for stability")
            for idx in large_scale_indices:
                if hasattr(scaler, 'scale_') and scaler.scale_ is not None:
                    scaler.scale_[idx] = 1e6   # Set maximum scale factor
    else:
        small_scale_indices = np.array([])
        large_scale_indices = np.array([])
    
    # Check for extreme means that could cause issues
    # CRITICAL FIX: Check for None before operations
    if scaler_mean is not None:
        extreme_mean_indices = np.where(np.abs(scaler_mean) > 1e6)[0]
        if len(extreme_mean_indices) > 0:
            print(f"     ⚠️ Warning: {len(extreme_mean_indices)} features have extreme mean values, capping for stability")
            for idx in extreme_mean_indices:
                if hasattr(scaler, 'mean_') and scaler.mean_ is not None:
                    scaler.mean_[idx] = np.sign(scaler.mean_[idx]) * 1e6  # Cap extreme means
    else:
        extreme_mean_indices = np.array([])
    
    preprocessors['scaler'] = scaler
    preprocessors['final_feature_names'] = feature_names
    
    # Store enhanced scaler statistics for validation
    preprocessors['scaler_stats'] = {
        'mean': scaler_mean.copy() if scaler_mean is not None else None,
        'scale': scaler_scale.copy() if scaler_scale is not None else None,
        'n_features': len(feature_names),
        'mean_range': [float(scaler_mean.min()), float(scaler_mean.max())] if scaler_mean is not None else None,
        'scale_range': [float(scaler_scale.min()), float(scaler_scale.max())] if scaler_scale is not None else None,
        'numerical_adjustments': {
            'small_scale_adjustments': len(small_scale_indices),
            'large_scale_adjustments': len(large_scale_indices),
            'extreme_mean_adjustments': len(extreme_mean_indices)
        }
    }
    
    if scaler_mean is not None and scaler_scale is not None:
        print(f"   • Scaler fitted on {len(scaler_mean)} features")
        print(f"   • Mean range: [{scaler_mean.min():.4f}, {scaler_mean.max():.4f}]")
        print(f"   • Scale range: [{scaler_scale.min():.4f}, {scaler_scale.max():.4f}]")
        
        # Report numerical stability enhancements
        if len(small_scale_indices) + len(large_scale_indices) + len(extreme_mean_indices) > 0:
            print(f"   • Numerical stability adjustments: {len(small_scale_indices)} small scales, {len(large_scale_indices)} large scales, {len(extreme_mean_indices)} extreme means")
        else:
            print(f"   • No numerical stability adjustments needed")
    else:
        print("   • Scaler fitted but statistics unavailable")

    # Final validation
    print("\n🔍 Final preprocessing validation...")
    print(f"   • Initial features: {len(preprocessors['initial_feature_names'])}")
    print(f"   • Final features: {len(preprocessors['final_feature_names'])}")
    print(f"   • Features dropped (high NA): {len(preprocessors['drop_cols_na'])}")
    print(f"   • Features dropped (constant): {np.sum(~preprocessors['keep_cols_std'])}")
    print(f"   • Imputation method: {preprocessors['imputation_method']}")
    print(f"   • Strict mode: {preprocessors['strict_mode']}")

    # Record final state
    state_manager.record_step('final', features_after_selection, feature_names)
    
    # Add state summary to preprocessors
    preprocessors['state_summary'] = state_manager.get_summary()
    
    print("\n🔍 Preprocessing State Summary:")
    print(state_manager.get_summary())
    
    print("\n🔍 Feature Management Report:")
    print(feature_manager.generate_compatibility_report())
    
    # Final comprehensive validation
    final_validation = comprehensive_data_validation(train_features, "final_training")
    validation_history.append(final_validation)
    
    # Store validation history in preprocessors for future reference
    preprocessors['validation_history'] = validation_history
    
    # Generate and display comprehensive report
    print("\n" + create_preprocessing_report(validation_history, feature_manager))
    
    print("=" * 60, "\n✅ PREPROCESSORS FITTED SUCCESSFULLY (STRICT MODE)", "\n" + "=" * 60)
    return preprocessors

def _validate_processed_data(data: np.ndarray, step_name: str) -> None:
    """Comprehensive validation of processed data to catch issues early."""
    print(f"🔍 Validating data after {step_name}...")
    
    if data is None:
        raise ValueError(f"Data is None after {step_name}")
    
    if data.size == 0:
        raise ValueError(f"Data is empty after {step_name}")
    
    # Check for NaN values with proper dtype handling
    # Ensure data is numeric before checking for NaN
    if hasattr(data, 'dtype'):
        if data.dtype == object or not np.issubdtype(data.dtype, np.number):
            raise ValueError(f"Non-numeric data type detected after {step_name}: {data.dtype}")
        else:
            data_numeric = data.astype(np.float32) if data.dtype != np.float32 else data
    else:
        data_numeric = data
    
    nan_count = np.sum(np.isnan(data_numeric))
    if nan_count > 0:
        nan_ratio = nan_count / data_numeric.size
        if nan_ratio > 0.5:  # More than 50% NaN
            raise ValueError(f"Excessive NaN values ({nan_ratio:.2%}) after {step_name}")
        elif nan_ratio > 0.2:  # More than 20% NaN but less than 50%
            pass  # Do not print or suppress, just proceed
    
    # Check for infinite values
    inf_count = np.sum(np.isinf(data_numeric))
    if inf_count > 0:
        raise ValueError(f"{inf_count} infinite values found after {step_name}")
    
    # Check for extremely large values
    finite_data = data[np.isfinite(data)]
    if len(finite_data) > 0:
        abs_max = np.abs(finite_data).max()
        if abs_max > 1e8:
            pass  # Do not print or suppress, just proceed
    
    # Check data range and distribution  
    if len(finite_data) > 0:
        pass  # No print, just validate
    
    # No print for success, just return

def apply_preprocessors(
    df: pd.DataFrame, preprocessors: Dict[str, Any]
) -> Tuple[np.ndarray, np.ndarray, List[str]]:
    """
    Apply fitted preprocessors to a new dataset with strict data leakage validation.
    Enhanced with comprehensive data validation to prevent NaN propagation.
    
    This function applies preprocessing transformations using parameters that were
    fitted ONLY on the training data, ensuring no data leakage occurs.
    """
    print(f"🔧 Applying preprocessors to dataset ({len(df)} samples)...")
    
    # Validate strict mode
    if not preprocessors.get('strict_mode', False):
        pass  # Do not print warning
    
    # Get state manager and feature manager for validation
    state_manager = preprocessors.get('state_manager')
    feature_manager = preprocessors.get('feature_manager')
    
    if state_manager is None:
        pass  # Do not print warning
    else:
        print("✅ State manager found - enabling preprocessing state validation")
    
    if feature_manager is None:
        # Create a temporary feature manager for this session
        initial_features = preprocessors.get('initial_feature_names', [])
        feature_manager = RobustFeatureManager(initial_features)
    else:
        print("✅ Feature manager found - enabling robust feature management")
    
    def validate_step_consistency(current_features: List[str], step_name: str):
        """Helper function to validate preprocessing step consistency."""
        if state_manager is not None:
            validation = state_manager.validate_consistency(current_features, step_name)
            if not validation['is_valid']:
                for issue in validation['issues']:
                    print(f"     - {issue}")
            if validation['warnings']:
                for warning in validation['warnings']:
                    print(f"     ⚠️ {warning}")
            return validation
        return None
    
    if df.empty:
        return np.array([]), np.array([]), preprocessors['final_feature_names']

    # Extract features and labels
    features, labels, _ = extract_labels_and_features(df)
    
    # Validate labels immediately
    if len(labels) > 0:
        nan_labels = np.sum(np.isnan(labels))
        if nan_labels > 0:
            pass  # Do not print warning
    
    # Validate feature consistency
    initial_features = preprocessors['initial_feature_names']
    missing_features = set(initial_features) - set(features.columns)
    extra_features = set(features.columns) - set(initial_features)
    
    # No warning prints for missing/extra features
    
    # Reindex to match training features (fill missing with 0)
    features = features.reindex(columns=initial_features, fill_value=0)
    print(f"✅ Feature alignment: {len(features.columns)} features")
    
    # Step 1: Handle infinite values (same as training)
    print("   • Step 1: Handling infinite values...")
    features = handle_infinite_values(features)
    # --- FIX: Ensure all columns are numeric and immediately impute any remaining NaNs with column median (or 0 if all NaN) ---
    for col in features.columns:
        features.loc[:, col] = pd.to_numeric(features[col], errors='coerce')
        if features[col].isna().any():
            median_val = features[col].median()
            if pd.isna(median_val):
                median_val = 0.0
            features[col] = features[col].fillna(median_val)
    # --- END FIX ---
    
    # Diagnostics: Print NaN counts per column after infinite value handling
    feature_values = features.values
    
    # Ensure feature_values is numeric before validation
    if feature_values.dtype == object or not np.issubdtype(feature_values.dtype, np.number):
        feature_values = feature_values.astype(np.float32)
    elif feature_values.dtype != np.float32:
        feature_values = feature_values.astype(np.float32)
    
    nan_counts = np.isnan(feature_values).sum(axis=0)
    nan_ratio = np.isnan(feature_values).mean(axis=0)
    # No print for NaN counts, just validate

    # CRITICAL: No automatic high-NA dropping in apply phase
    # We must maintain the same feature set that the imputer was trained on
    # Only apply the training-derived drops to ensure feature consistency
    print("   • Skipping automatic high-NA dropping to maintain imputer feature consistency")

    _validate_processed_data(feature_values, "infinite value handling")
    
    # Validate state after infinite value handling
    validate_step_consistency(list(features.columns), 'infinite_handling')
    
    # Step 2: Drop high-NA features (using training-derived list)
    print("   • Step 2: Dropping high-NA features...")
    drop_cols_na = preprocessors['drop_cols_na']
    if drop_cols_na:
        features_before = features.copy()
        features = safe_drop_features(features, drop_cols_na, "high-NA features")
        track_feature_changes(features_before, features, "high-NA feature removal")
    
    # Validate state after NA drop
    validate_step_consistency(list(features.columns), 'na_drop')
    
    # Step 3: Apply winsorization (using training-derived bounds)
    print("   • Step 3: Applying winsorization...")
    lower, upper = preprocessors['winsorize_quantiles']
    features = features.clip(lower=lower, upper=upper, axis=1)
    print(f"     - Applied winsorization bounds (from training)")
    
    # Validate after winsorization
    feature_values = features.values
    _validate_processed_data(feature_values, "winsorization")
    
    # Validate state after winsorization
    validate_step_consistency(list(features.columns), 'winsorization')
    
    # Step 3.5: Ensure data quality for imputation
    print("   • Step 3.5: Ensuring data quality...")
    features = ensure_data_quality_for_imputation(features)
    
    # Validate after data quality checks
    feature_values = features.values
    _validate_processed_data(feature_values, "data quality checks")
    
    # Step 4: Apply imputation (using training-fitted imputer)
    print("   • Step 4: Applying imputation...")
    imputer = preprocessors['imputer']
    if imputer:
        # Apply the same pre-imputation strategy as used during training
        if config.IMPUTATION_METHOD == 'mice':
            pre_impute_threshold = getattr(config, 'PRE_IMPUTE_THRESHOLD', 0.3)
            pre_impute_method = getattr(config, 'PRE_IMPUTE_METHOD', 'median')
            
            high_na_cols = features.columns[features.isna().mean() > pre_impute_threshold].tolist()
            if high_na_cols:
                print(f"     - Pre-imputing {len(high_na_cols)} features with >{pre_impute_threshold*100:.0f}% missing values using {pre_impute_method}...")
                for col in high_na_cols:
                    if pre_impute_method == 'median':
                        fill_val = features[col].median()
                    elif pre_impute_method == 'mean':
                        fill_val = features[col].mean()
                    else:
                        fill_val = features[col].median()  # Default to median
                    
                    # Handle case where fill_val might be NaN (all values missing)
                    if pd.isna(fill_val):
                        fill_val = 0.0
                    
                    features[col] = features[col].fillna(fill_val)
        
        features_imp = imputer.transform(features)
        # Ensure we have a proper numpy array for DataFrame construction
        # Check if result is a scipy sparse matrix and convert to dense array
        if scipy.sparse.issparse(features_imp):
            # Handle scipy sparse matrix by converting to dense array
            features_imp = features_imp.toarray()  # type: ignore
        features_imp = np.asarray(features_imp, dtype=np.float32)
        
        # Validate imputed data
        _validate_processed_data(features_imp, "imputation")
        
        features = pd.DataFrame(
            data=features_imp,
            columns=features.columns,
            index=features.index
        )
        # Diagnostic: print number of missing values remaining
        n_missing = features.isna().sum().sum()
        print(f"     - After imputation: {n_missing} missing values remain in data")
    else:
        features = features.ffill().bfill().fillna(0)
        # Validate after simple imputation
        feature_values = features.values
        _validate_processed_data(feature_values, "simple imputation")
    
    # Step 5: Filter constant features (using training-derived mask)
    print("   • Step 5: Filtering constant features...")
    keep_cols_std = preprocessors['keep_cols_std']
    
    # Validate that the keep_cols_std mask is compatible with current features
    if len(keep_cols_std) != len(features.columns):
        print(f"     - Feature count mismatch: {len(features.columns)} current vs {len(keep_cols_std)} in mask")
        # Create a new mask based on current features and the original feature names
        initial_features = preprocessors['initial_feature_names']
        current_keep_mask = []
        for col in features.columns:
            if col in initial_features:
                original_idx = initial_features.index(col)
                if original_idx < len(keep_cols_std):
                    current_keep_mask.append(keep_cols_std.iloc[original_idx] if hasattr(keep_cols_std, 'iloc') else keep_cols_std[original_idx])
                else:
                    current_keep_mask.append(True)  # Keep if not in original mask
            else:
                current_keep_mask.append(True)  # Keep new features
        current_keep_mask = pd.Series(current_keep_mask, index=features.columns)
    else:
        current_keep_mask = keep_cols_std
    
    features_before_const = features.copy()
    features = features.loc[:, current_keep_mask]
    track_feature_changes(features_before_const, features, "constant feature removal")
    dropped_constant = np.sum(~current_keep_mask)
    if dropped_constant > 0:
        print(f"     - Dropped {dropped_constant} constant features (from training)")
    
    # Step 6: Feature selection (using training-derived list)
    print("   • Step 6: Applying feature selection...")
    selected_features = preprocessors['selected_features']
    
    # Use feature manager for robust feature validation
    validation_result = feature_manager.validate_pipeline_consistency(selected_features, tolerance_ratio=0.5)
    
    if not validation_result['is_consistent']:
        print(f"     - ⚠️ Feature consistency check failed:")
        print(f"       * Missing: {len(validation_result['missing_features'])} features ({validation_result['missing_ratio']:.1%})")
        print(f"       * Extra: {len(validation_result['extra_features'])} features")
        
        if 'recovery_strategies' in validation_result:
            print(f"     - 💡 Recovery suggestions:")
            for recommendation in validation_result['recommendations'][:3]:  # Show top 3
                print(f"       * {recommendation}")
        
        # If too many features are missing, this could indicate a serious preprocessing mismatch
        if validation_result['missing_ratio'] > 0.5:  # More than 50% of features missing
            print(f"     - 🔍 Detailed missing feature analysis:")
            if 'categorized_missing' in validation_result:
                for category, features in validation_result['categorized_missing'].items():
                    if features:
                        print(f"       * {category}: {len(features)} features")
            
            raise ValueError(
                f"Feature selection failure: {validation_result['missing_ratio']:.1%} of selected features are missing. "
                f"This indicates a serious preprocessing pipeline mismatch. See analysis above for details."
            )
    
    # Find selected features that still exist in current dataframe
    available_selected_features = [col for col in selected_features if col in features.columns]
    missing_selected_features = [col for col in selected_features if col not in features.columns]
    
    if missing_selected_features:
        print(f"     - Warning: {len(missing_selected_features)} selected features missing: {missing_selected_features[:5]}{'...' if len(missing_selected_features) > 5 else ''}")
    
    features_before_selection = features.copy()
    features = features[available_selected_features]
    track_feature_changes(features_before_selection, features, "feature selection")
    print(f"     - Selected {len(available_selected_features)} features (from training)")
    
    # Validate before standardization
    feature_values = features.values.astype(np.float32)
    _validate_processed_data(feature_values, "feature selection")
    
    # Step 7: Apply standardization (CRITICAL - using training-fitted scaler)
    print("   • Step 7: Applying standardization...")
    scaler = preprocessors['scaler']
    
    # Validate scaler was fitted
    if not hasattr(scaler, 'mean_') or not hasattr(scaler, 'scale_'):
        raise RuntimeError("Scaler was not fitted - missing mean_ or scale_ attributes")
    
    # Additional validation: check feature count consistency
    scaler_mean = scaler.mean_
    scaler_scale = scaler.scale_
    
    if scaler_mean is not None and len(features.columns) != len(scaler_mean):
        raise RuntimeError(
            f"Feature count mismatch: {len(features.columns)} features in data, "
            f"but scaler was fitted on {len(scaler_mean)} features"
        )
    
    print(f"     - Applying standardization (fitted on training data)")
    if scaler_mean is not None and scaler_scale is not None:
        print(f"       * Using training mean: [{scaler_mean.min():.4f}, {scaler_mean.max():.4f}]")
        print(f"       * Using training scale: [{scaler_scale.min():.4f}, {scaler_scale.max():.4f}]")
    else:
        print("       * Scaler statistics unavailable for display")
    
    processed_data = scaler.transform(features.values.astype(np.float32))
    
    # Final comprehensive validation
    _validate_processed_data(processed_data, "standardization (final)")
    
    # Final validation
    print(f"✅ Preprocessing complete:")
    print(f"   • Input shape: {df.shape}")
    print(f"   • Output shape: {processed_data.shape}")
    print(f"   • Labels shape: {labels.shape}")
    print(f"   • Features used: {len(preprocessors['final_feature_names'])}")
    
    # Additional final checks
    if processed_data.size > 0:
        finite_ratio = np.sum(np.isfinite(processed_data)) / processed_data.size
        print(f"   • Finite values ratio: {finite_ratio:.4f}")
        
        if finite_ratio < 0.95:  # Less than 95% finite values
            print(f"⚠️ Warning: Only {finite_ratio:.2%} of values are finite")
        
        # Check data distribution after preprocessing
        finite_data = processed_data[np.isfinite(processed_data)]
        if len(finite_data) > 0:
            data_mean = finite_data.mean()
            data_std = finite_data.std()
            print(f"   • Final data mean: {data_mean:.4f}, std: {data_std:.4f}")
    
    # Final constant feature check to prevent correlation warnings
    print("🔍 Final constant feature check...")
    # Use the same threshold that was used during training to ensure consistency
    constant_feature_check_threshold = preprocessors.get('constant_threshold_used', 1e-10)
    
    # Calculate variance for each feature column in the final processed data
    final_variances = np.var(processed_data, axis=0)
    constant_feature_mask = final_variances < constant_feature_check_threshold
    
    if np.any(constant_feature_mask):
        constant_feature_indices = np.where(constant_feature_mask)[0]
        constant_feature_names = [features.columns[i] for i in constant_feature_indices if i < len(features.columns)]
        
        print(f"⚠️ Found {len(constant_feature_indices)} near-constant features in final data:")
        print(f"   Features: {constant_feature_names[:5]}{'...' if len(constant_feature_names) > 5 else ''}")
        
        # Remove these features from the processed data
        valid_feature_mask = ~constant_feature_mask
        processed_data = processed_data[:, valid_feature_mask]
        
        # Update feature names to match the filtered data
        updated_feature_names = [name for i, name in enumerate(features.columns) if i < len(valid_feature_mask) and valid_feature_mask[i]]
        actual_final_features = updated_feature_names
        
        print(f"   Removed {len(constant_feature_indices)} constant features from final output")
        print(f"   Final feature count: {len(actual_final_features)}")
    else:
        # Update final feature names to reflect actually available features
        actual_final_features = list(features.columns)
        print(f"✅ No constant features detected in final data")
    
    # Ensure final processed_data is consistently float32
    if processed_data.dtype != np.float32:
        print(f"⚠️ Converting final processed_data from {processed_data.dtype} to float32")
        processed_data = processed_data.astype(np.float32)
    
    # Ensure labels are consistently float32
    if labels.dtype != np.float32:
        print(f"⚠️ Converting labels from {labels.dtype} to float32")
        labels = labels.astype(np.float32)
    
    return processed_data, labels, actual_final_features 

class PreprocessingStateManager:
    """
    Manages preprocessing state to ensure consistency between training and application phases.
    
    This class tracks feature transformations throughout the preprocessing pipeline and
    ensures that the same sequence of operations is applied during both training fitting
    and subsequent application to validation/test data.
    """
    
    def __init__(self):
        self.state = {
            'initial_features': None,
            'features_after_infinite_handling': None,
            'features_after_na_drop': None,
            'features_after_winsorization': None,
            'features_after_quality_check': None,
            'features_after_imputation': None,
            'features_after_constant_filter': None,
            'features_after_selection': None,
            'final_features': None,
            'step_history': [],
            'preprocessing_params': {}
        }
    
    def record_step(self, step_name: str, features_before: List[str], features_after: List[str], params: Dict[str, Any] = {}):
        """Record a preprocessing step with before/after feature lists."""
        step_info = {
            'step_name': step_name,
            'features_before': features_before.copy() if isinstance(features_before, list) else list(features_before),
            'features_after': features_after.copy() if isinstance(features_after, list) else list(features_after),
            'features_dropped': list(set(features_before) - set(features_after)),
            'features_added': list(set(features_after) - set(features_before)),
            'params': params or {}
        }
        self.state['step_history'].append(step_info)
        
        # Update state features
        if step_name == 'initial':
            self.state['initial_features'] = features_after
        elif step_name == 'infinite_handling':
            self.state['features_after_infinite_handling'] = features_after
        elif step_name == 'na_drop':
            self.state['features_after_na_drop'] = features_after
        elif step_name == 'winsorization':
            self.state['features_after_winsorization'] = features_after
        elif step_name == 'quality_check':
            self.state['features_after_quality_check'] = features_after
        elif step_name == 'imputation':
            self.state['features_after_imputation'] = features_after
        elif step_name == 'constant_filter':
            self.state['features_after_constant_filter'] = features_after
        elif step_name == 'selection':
            self.state['features_after_selection'] = features_after
        elif step_name == 'final':
            self.state['final_features'] = features_after
    
    def validate_consistency(self, current_features: List[str], expected_step: str) -> Dict[str, Any]:
        """
        Validate that current features are consistent with expected preprocessing state.
        
        Args:
            current_features: Current feature list
            expected_step: Expected preprocessing step name
            
        Returns:
            Dictionary with validation results
        """
        validation_result = {
            'is_valid': True,
            'issues': [],
            'warnings': [],
            'expected_features': None,
            'missing_features': [],
            'extra_features': []
        }
        
        # Get expected features for this step
        expected_features = None
        if expected_step in ['initial', 'infinite_handling']:
            expected_features = self.state.get('initial_features')
        elif expected_step == 'na_drop':
            expected_features = self.state.get('features_after_na_drop')
        elif expected_step == 'winsorization':
            expected_features = self.state.get('features_after_winsorization')
        elif expected_step == 'quality_check':
            expected_features = self.state.get('features_after_quality_check')
        elif expected_step == 'imputation':
            expected_features = self.state.get('features_after_imputation')
        elif expected_step == 'constant_filter':
            expected_features = self.state.get('features_after_constant_filter')
        elif expected_step == 'selection':
            expected_features = self.state.get('features_after_selection')
        elif expected_step == 'final':
            expected_features = self.state.get('final_features')
        
        if expected_features is None:
            validation_result['warnings'].append(f"No expected features recorded for step '{expected_step}'")
            return validation_result
        
        validation_result['expected_features'] = expected_features
        
        # Compare current vs expected
        current_set = set(current_features)
        expected_set = set(expected_features)
        
        missing_features = expected_set - current_set
        extra_features = current_set - expected_set
        
        validation_result['missing_features'] = list(missing_features)
        validation_result['extra_features'] = list(extra_features)
        
        if missing_features:
            validation_result['is_valid'] = False
            validation_result['issues'].append(f"Missing {len(missing_features)} expected features")
        
        if extra_features:
            validation_result['warnings'].append(f"Found {len(extra_features)} unexpected features")
        
        return validation_result
    
    def get_summary(self) -> str:
        """Get a summary of the preprocessing state."""
        if not self.state['step_history']:
            return "No preprocessing steps recorded"
        
        summary = ["Preprocessing State Summary:"]
        for i, step in enumerate(self.state['step_history']):
            features_before = len(step['features_before'])
            features_after = len(step['features_after'])
            features_dropped = len(step['features_dropped'])
            summary.append(f"  {i+1}. {step['step_name']}: {features_before} → {features_after} features (-{features_dropped})")
        
        return "\n".join(summary) 

class RobustFeatureManager:
    """
    Comprehensive feature management system for preprocessing pipeline.
    
    This class provides robust feature handling including:
    - Feature lifecycle tracking
    - Missing feature recovery strategies
    - Feature compatibility validation
    - Pipeline state consistency checks
    """
    
    def __init__(self, initial_features: List[str]):
        self.initial_features = initial_features.copy()
        self.feature_history = {
            'initial': initial_features.copy()
        }
        self.dropped_features = {
            'infinite_handling': [],
            'na_drop': [],
            'quality_check': [],
            'constant_filter': [],
            'selection': []
        }
        self.recovery_strategies = {}
        self.compatibility_matrix = {}
        
    def register_step(self, step_name: str, features_before: List[str], features_after: List[str], 
                     dropped_reason: str = "unknown"):
        """Register a preprocessing step and track feature changes."""
        self.feature_history[step_name] = features_after.copy()
        
        # Track dropped features
        dropped_in_step = list(set(features_before) - set(features_after))
        if dropped_in_step:
            self.dropped_features[step_name] = dropped_in_step
            print(f"   📝 Feature manager: {len(dropped_in_step)} features dropped in {step_name}: {dropped_reason}")
    
    def get_feature_status(self, feature_name: str) -> Dict[str, Any]:
        """Get the complete status and history of a specific feature."""
        status = {
            'exists': feature_name in self.get_current_features(),
            'initial': feature_name in self.initial_features,
            'drop_step': None,
            'drop_reason': None,
            'recovery_possible': False
        }
        
        # Find where the feature was dropped
        for step, dropped_list in self.dropped_features.items():
            if feature_name in dropped_list:
                status['drop_step'] = step
                status['recovery_possible'] = step in ['na_drop', 'constant_filter']
                break
        
        return status
    
    def get_current_features(self) -> List[str]:
        """Get the current list of features after all registered steps."""
        if not self.feature_history:
            return self.initial_features.copy()
        
        # Get the last step
        last_step = list(self.feature_history.keys())[-1]
        return self.feature_history[last_step].copy()
    
    def find_missing_features(self, required_features: List[str]) -> Dict[str, List[str]]:
        """Find missing features and categorize them by drop reason."""
        current_features = set(self.get_current_features())
        missing_features = [f for f in required_features if f not in current_features]
        
        categorized_missing = {
            'never_existed': [],
            'infinite_handling': [],
            'na_drop': [],
            'quality_check': [],
            'constant_filter': [],
            'selection': [],
            'unknown': []
        }
        
        for feature in missing_features:
            if feature not in self.initial_features:
                categorized_missing['never_existed'].append(feature)
            else:
                found_drop_step = False
                for step, dropped_list in self.dropped_features.items():
                    if feature in dropped_list:
                        categorized_missing[step].append(feature)
                        found_drop_step = True
                        break
                
                if not found_drop_step:
                    categorized_missing['unknown'].append(feature)
        
        return categorized_missing
    
    def suggest_recovery_strategies(self, missing_features: List[str]) -> Dict[str, str]:
        """Suggest recovery strategies for missing features."""
        strategies = {}
        
        for feature in missing_features:
            status = self.get_feature_status(feature)
            
            if not status['initial']:
                strategies[feature] = "Feature never existed in original dataset - cannot recover"
            elif status['drop_step'] == 'infinite_handling':
                strategies[feature] = "Dropped due to infinite values - recovery not recommended"
            elif status['drop_step'] == 'na_drop':
                strategies[feature] = "Dropped due to high NA ratio - consider imputation or relaxed threshold"
            elif status['drop_step'] == 'quality_check':
                strategies[feature] = "Dropped due to quality issues - review data quality constraints"
            elif status['drop_step'] == 'constant_filter':
                strategies[feature] = "Dropped due to low variance - consider relaxed variance threshold"
            elif status['drop_step'] == 'selection':
                strategies[feature] = "Removed by feature selection - update selection criteria"
            else:
                strategies[feature] = "Unknown drop reason - manual investigation required"
        
        return strategies
    
    def validate_pipeline_consistency(self, expected_features: List[str], 
                                    tolerance_ratio: float = 0.1) -> Dict[str, Any]:
        """
        Validate that the current feature set is consistent with expectations.
        
        Args:
            expected_features: Features expected to be available
            tolerance_ratio: Acceptable ratio of missing features (0.1 = 10%)
        """
        current_features = set(self.get_current_features())
        expected_set = set(expected_features)
        
        missing_features = list(expected_set - current_features)
        extra_features = list(current_features - expected_set)
        
        missing_ratio = len(missing_features) / len(expected_features) if expected_features else 0
        
        result = {
            'is_consistent': missing_ratio <= tolerance_ratio,
            'missing_features': missing_features,
            'extra_features': extra_features,
            'missing_ratio': missing_ratio,
            'tolerance_ratio': tolerance_ratio,
            'recommendations': []
        }
        
        if missing_ratio > tolerance_ratio:
            categorized_missing = self.find_missing_features(missing_features)
            strategies = self.suggest_recovery_strategies(missing_features)
            
            result['categorized_missing'] = categorized_missing
            result['recovery_strategies'] = strategies
            
            # Generate recommendations
            if categorized_missing['na_drop']:
                result['recommendations'].append("Consider relaxing MAX_NA_RATIO threshold")
            if categorized_missing['constant_filter']:
                result['recommendations'].append("Consider relaxing CONSTANT_FEATURE_THRESHOLD")
            if categorized_missing['never_existed']:
                result['recommendations'].append("Update expected feature list - some features never existed")
        
        return result
    
    def generate_compatibility_report(self) -> str:
        """Generate a comprehensive compatibility report."""
        report = ["Feature Management Compatibility Report", "=" * 45]
        
        current_features = self.get_current_features()
        report.append(f"Initial Features: {len(self.initial_features)}")
        report.append(f"Current Features: {len(current_features)}")
        report.append(f"Total Dropped: {len(self.initial_features) - len(current_features)}")
        
        report.append("\nFeature Drops by Step:")
        total_dropped = 0
        for step, dropped_list in self.dropped_features.items():
            if dropped_list:
                report.append(f"  {step}: {len(dropped_list)} features")
                total_dropped += len(dropped_list)
        
        if total_dropped != (len(self.initial_features) - len(current_features)):
            report.append(f"⚠️ Inconsistency detected: {total_dropped} vs {len(self.initial_features) - len(current_features)}")
        
        report.append(f"\nFeature Retention Rate: {len(current_features)/len(self.initial_features)*100:.1f}%")
        
        return "\n".join(report) 

def comprehensive_data_validation(df: pd.DataFrame, step_name: str) -> Dict[str, Any]:
    """
    Perform comprehensive validation of data quality at any preprocessing step.
    
    Args:
        df: DataFrame to validate
        step_name: Name of the preprocessing step for context
        
    Returns:
        Dictionary with detailed validation results
    """
    validation_result = {
        'step_name': step_name,
        'total_features': len(df.columns),
        'total_samples': len(df),
        'data_types': {},
        'missing_values': {},
        'infinite_values': {},
        'data_ranges': {},
        'quality_issues': [],
        'recommendations': []
    }
    
    # Data type analysis
    for dtype in df.dtypes.unique():
        cols_of_type = df.select_dtypes(include=[dtype]).columns.tolist()
        validation_result['data_types'][str(dtype)] = len(cols_of_type)
    
    # Missing value analysis
    missing_counts = df.isnull().sum()
    missing_ratios = df.isnull().mean()
    
    validation_result['missing_values'] = {
        'total_missing_cells': missing_counts.sum(),
        'features_with_missing': (missing_counts > 0).sum(),
        'worst_feature_missing_ratio': missing_ratios.max(),
        'features_over_50pct_missing': (missing_ratios > 0.5).sum(),
        'features_over_90pct_missing': (missing_ratios > 0.9).sum()
    }
    
    # Infinite value analysis
    numeric_df = df.select_dtypes(include=[np.number])
    if not numeric_df.empty:
        inf_counts = np.isinf(numeric_df.values).sum(axis=0)
        total_inf = inf_counts.sum()
        validation_result['infinite_values'] = {
            'total_infinite_cells': int(total_inf),
            'features_with_infinite': int((inf_counts > 0).sum()),
            'worst_feature_inf_count': int(inf_counts.max()) if len(inf_counts) > 0 else 0
        }
        
        # Data range analysis for numeric features
        validation_result['data_ranges'] = {
            'min_value': float(numeric_df.min().min()) if not numeric_df.empty else None,
            'max_value': float(numeric_df.max().max()) if not numeric_df.empty else None,
            'features_with_negative': (numeric_df.min() < 0).sum(),
            'features_with_zero_variance': (numeric_df.std() == 0).sum()
        }
    
    # Identify quality issues
    if validation_result['missing_values']['features_over_90pct_missing'] > 0:
        validation_result['quality_issues'].append(
            f"{validation_result['missing_values']['features_over_90pct_missing']} features have >90% missing values"
        )
    
    if validation_result['infinite_values']['total_infinite_cells'] > 0:
        validation_result['quality_issues'].append(
            f"{validation_result['infinite_values']['total_infinite_cells']} infinite values detected"
        )
    
    if 'features_with_zero_variance' in validation_result['data_ranges']:
        zero_var_count = validation_result['data_ranges']['features_with_zero_variance']
        if zero_var_count > 0:
            validation_result['quality_issues'].append(
                f"{zero_var_count} features have zero variance"
            )
    
    # Generate recommendations
    if validation_result['missing_values']['features_with_missing'] > 0:
        validation_result['recommendations'].append("Consider imputation for features with missing values")
    
    if validation_result['infinite_values']['total_infinite_cells'] > 0:
        validation_result['recommendations'].append("Handle infinite values before proceeding")
    
    return validation_result

def log_validation_summary(validation_result: Dict[str, Any], verbose: bool = True) -> None:
    """Log a summary of validation results."""
    step = validation_result['step_name']
    print(f"   🔍 Validation for {step}:")
    print(f"     - Data shape: {validation_result['total_samples']} samples × {validation_result['total_features']} features")
    
    # Missing values summary
    missing = validation_result['missing_values']
    if missing['total_missing_cells'] > 0:
        print(f"     - Missing values: {missing['total_missing_cells']} cells ({missing['features_with_missing']} features affected)")
        if missing['worst_feature_missing_ratio'] > 0.5:
            print(f"       ⚠️ Worst feature: {missing['worst_feature_missing_ratio']:.1%} missing")
    else:
        print(f"     - Missing values: None ✅")
    
    # Infinite values summary
    infinite = validation_result['infinite_values']
    if infinite['total_infinite_cells'] > 0:
        print(f"     - Infinite values: {infinite['total_infinite_cells']} cells ({infinite['features_with_infinite']} features affected)")
    else:
        print(f"     - Infinite values: None ✅")
    
    # Quality issues
    if validation_result['quality_issues']:
        print(f"     - Quality issues:")
        for issue in validation_result['quality_issues']:
            print(f"       ⚠️ {issue}")
    
    # Recommendations
    if validation_result['recommendations'] and verbose:
        print(f"     - Recommendations:")
        for rec in validation_result['recommendations']:
            print(f"       💡 {rec}")

def enhanced_feature_tracking(df_before: pd.DataFrame, df_after: pd.DataFrame, 
                            step_name: str, feature_manager: Optional['RobustFeatureManager'] = None) -> None:
    """
    Enhanced feature tracking with detailed analysis of changes.
    
    Args:
        df_before: DataFrame before the preprocessing step
        df_after: DataFrame after the preprocessing step
        step_name: Name of the preprocessing step
        feature_manager: Optional feature manager for robust tracking
    """
    features_before = set(df_before.columns)
    features_after = set(df_after.columns)
    
    dropped_features = features_before - features_after
    added_features = features_after - features_before
    common_features = features_before & features_after
    
    print(f"   📊 Feature tracking for {step_name}:")
    print(f"     - Before: {len(features_before)} features")
    print(f"     - After: {len(features_after)} features")
    print(f"     - Net change: {len(features_after) - len(features_before):+d} features")
    
    if dropped_features:
        print(f"     - Dropped: {len(dropped_features)} features")
        if len(dropped_features) <= 10:
            print(f"       * {list(dropped_features)}")
        else:
            sample_dropped = list(dropped_features)[:10]
            print(f"       * {sample_dropped} ... and {len(dropped_features) - 10} more")
    
    if added_features:
        print(f"     - Added: {len(added_features)} features")
        if len(added_features) <= 10:
            print(f"       * {list(added_features)}")
        else:
            sample_added = list(added_features)[:10]
            print(f"       * {sample_added} ... and {len(added_features) - 10} more")
    
    # Analyze data changes for common features
    if common_features:
        data_changes = []
        for feature in list(common_features)[:5]:  # Check first 5 common features
            before_vals = df_before[feature].values
            after_vals = df_after[feature].values
            before_vals_np = np.asarray(before_vals)
            after_vals_np = np.asarray(after_vals)
            if not np.array_equal(before_vals_np, after_vals_np, equal_nan=True):
                before_mean = np.nanmean(before_vals_np) if len(before_vals_np) > 0 else 0
                after_mean = np.nanmean(after_vals_np) if len(after_vals_np) > 0 else 0
                if not np.isnan(before_mean) and not np.isnan(after_mean):
                    data_changes.append(f"{feature}: mean {before_mean:.3f} → {after_mean:.3f}")
        
        if data_changes:
            print(f"     - Data changes detected in {len(data_changes)} features:")
            for change in data_changes[:3]:  # Show first 3
                print(f"       * {change}")
    
    # Update feature manager if provided
    if feature_manager:
        feature_manager.register_step(step_name, list(features_before), list(features_after))

def create_preprocessing_report(validation_history: List[Dict[str, Any]], 
                              feature_manager: Optional['RobustFeatureManager'] = None) -> str:
    """
    Create a comprehensive preprocessing report.
    
    Args:
        validation_history: List of validation results from each step
        feature_manager: Optional feature manager for additional insights
        
    Returns:
        Formatted report string
    """
    report = ["=" * 60, "COMPREHENSIVE PREPROCESSING REPORT", "=" * 60, ""]
    
    if not validation_history:
        report.append("No validation data available.")
        return "\n".join(report)
    
    # Overall summary
    initial_step = validation_history[0]
    final_step = validation_history[-1]
    
    report.extend([
        "📊 OVERALL SUMMARY",
        "-" * 20,
        f"Initial data: {initial_step['total_samples']} samples × {initial_step['total_features']} features",
        f"Final data: {final_step['total_samples']} samples × {final_step['total_features']} features",
        f"Feature reduction: {initial_step['total_features'] - final_step['total_features']} features removed",
        f"Retention rate: {final_step['total_features']/initial_step['total_features']*100:.1f}%",
        ""
    ])
    
    # Step-by-step analysis
    report.extend(["📋 STEP-BY-STEP ANALYSIS", "-" * 25])
    
    for i, step_validation in enumerate(validation_history):
        step_name = step_validation['step_name']
        report.append(f"{i+1}. {step_name.upper()}")
        
        # Feature count
        report.append(f"   Features: {step_validation['total_features']}")
        
        # Quality metrics
        missing = step_validation['missing_values']['total_missing_cells']
        infinite = step_validation['infinite_values']['total_infinite_cells']
        
        if missing > 0 or infinite > 0:
            report.append(f"   Quality: {missing} missing, {infinite} infinite values")
        else:
            report.append("   Quality: ✅ No issues")
        
        # Issues
        if step_validation['quality_issues']:
            report.append("   Issues:")
            for issue in step_validation['quality_issues']:
                report.append(f"     ⚠️ {issue}")
        
        report.append("")
    
    # Feature manager report
    if feature_manager:
        report.extend(["🔧 FEATURE MANAGEMENT REPORT", "-" * 30])
        report.append(feature_manager.generate_compatibility_report())
        report.append("")
    
    # Final quality assessment
    final_quality = "✅ EXCELLENT"
    if final_step['quality_issues']:
        final_quality = "⚠️ NEEDS ATTENTION"
    elif final_step['missing_values']['total_missing_cells'] > 0:
        final_quality = "✅ GOOD"
    
    report.extend([
        "🎯 FINAL ASSESSMENT",
        "-" * 20,
        f"Data Quality: {final_quality}",
        f"Ready for modeling: {len(final_step['quality_issues']) == 0}",
        ""
    ])
    
    report.append("=" * 60)
    
    return "\n".join(report) 