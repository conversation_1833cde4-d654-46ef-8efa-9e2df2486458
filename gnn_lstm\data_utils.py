#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data Utilities Module

This module provides data processing utilities for the GNN-LSTM financial crisis
prediction system, including:

1. Temporal cross-validation with data leakage prevention
2. Class weight calculation for imbalanced datasets
3. Data leakage validation and detection
4. Temporal data splitting with embargo periods
5. Dataset preparation and validation utilities

All utilities ensure proper temporal ordering and prevent data leakage through
comprehensive validation and embargo mechanisms.
"""

import sys
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import torch
from sklearn.model_selection import TimeSeriesSplit
from sklearn.utils.class_weight import compute_class_weight

from . import config


def calculate_class_weights(
    labels: np.ndarray,
    method: str = 'balanced',
    epsilon: Optional[float] = None,
) -> np.ndarray:
    """
    Calculate class weights for imbalanced datasets.
    
    Args:
        labels: Array of class labels
        method: Method for weight calculation ('balanced' or 'custom')
        epsilon: Small value for numerical stability
        
    Returns:
        Array of class weights
    """
    if epsilon is None:
        epsilon = getattr(config, 'CLASS_WEIGHT_EPSILON', 1e-8)
    if epsilon is None:
        epsilon = 1e-8
    epsilon = float(epsilon)
    
    labels = np.asarray(labels).flatten()
    unique_classes = np.unique(labels)
    
    if len(unique_classes) < 2:
        # Single class case
        return np.ones(len(unique_classes))
    
    if method == 'balanced':
        weights = compute_class_weight('balanced', classes=unique_classes, y=labels)
    else:
        # Custom inverse frequency weighting
        class_counts = np.bincount(labels)
        total_samples = len(labels)
        weights = total_samples / (len(unique_classes) * (class_counts + epsilon))
    
    return weights


def get_sampler_weights(
    labels: np.ndarray,
    class_weights: Optional[np.ndarray] = None,
) -> torch.Tensor:
    """
    Get sample weights for WeightedRandomSampler.
    
    Args:
        labels: Array of class labels
        class_weights: Pre-computed class weights (if None, will calculate)
        
    Returns:
        Tensor of sample weights
    """
    labels = np.asarray(labels).flatten()
    
    if class_weights is None:
        class_weights = calculate_class_weights(labels)
    
    # Map class weights to sample weights
    sample_weights = np.zeros(len(labels))
    unique_classes = np.unique(labels)
    
    for i, class_label in enumerate(unique_classes):
        mask = labels == class_label
        sample_weights[mask] = class_weights[i]
    
    return torch.FloatTensor(sample_weights)


def create_temporal_splits(
    data: pd.DataFrame,
    time_col: str,
    n_splits: int = 5,
    test_size: float = 0.15,
    embargo_periods: int = 3,
    purge_samples: int = 90,
    min_test_samples: int = 5,
    min_positive_samples: int = 2,
    response_col: str = 'Response',
) -> List[Tuple[np.ndarray, np.ndarray]]:
    """
    Create temporal cross-validation splits with embargo and purging.
    
    This function creates chronologically ordered train-test splits with proper
    embargo periods to prevent data leakage in time series prediction.
    
    Args:
        data: DataFrame with temporal data
        time_col: Name of time column
        n_splits: Number of CV splits
        test_size: Proportion for test set
        embargo_periods: Number of periods for embargo
        purge_samples: Number of samples to purge around test set
        min_test_samples: Minimum samples required in test set
        min_positive_samples: Minimum positive samples in test set
        response_col: Name of response column
        
    Returns:
        List of (train_indices, test_indices) tuples
    """
    # Sort by time
    data_sorted = data.sort_values(time_col).reset_index(drop=True)
    total_samples = len(data_sorted)
    
    # Calculate test set size
    test_samples = max(min_test_samples, int(total_samples * test_size))
    
    # Create time series splits
    tscv = TimeSeriesSplit(n_splits=n_splits, test_size=test_samples)
    
    valid_splits = []
    
    for train_idx, test_idx in tscv.split(data_sorted):
        # Apply embargo and purging
        train_idx_purged, test_idx_purged = apply_embargo_and_purge(
            train_idx, test_idx, embargo_periods, purge_samples
        )
        
        # Validate split quality
        if validate_split_quality(
            data_sorted, train_idx_purged, test_idx_purged,
            response_col, min_test_samples, min_positive_samples
        ):
            valid_splits.append((train_idx_purged, test_idx_purged))
    
    if not valid_splits:
        raise ValueError("No valid temporal splits could be created with the given constraints")
    
    return valid_splits


def apply_embargo_and_purge(
    train_indices: np.ndarray,
    test_indices: np.ndarray,
    embargo_periods: int,
    purge_samples: int,
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Apply embargo and purging to train-test splits.
    
    Args:
        train_indices: Training set indices
        test_indices: Test set indices
        embargo_periods: Number of periods for embargo
        purge_samples: Number of samples to purge
        
    Returns:
        Tuple of (purged_train_indices, purged_test_indices)
    """
    # Convert to sets for easier manipulation
    train_set = set(train_indices)
    test_set = set(test_indices)
    
    # Find the boundary between train and test
    max_train_idx = max(train_indices) if train_indices.size > 0 else -1
    min_test_idx = min(test_indices) if test_indices.size > 0 else float('inf')
    
    # Apply purging: remove samples around the boundary
    purge_start = max_train_idx - purge_samples // 2
    
    # Remove purged samples from training set
    train_set = {idx for idx in train_set if idx < purge_start}
    
    # Apply embargo: remove recent samples from training set
    embargo_cutoff = min_test_idx - embargo_periods
    train_set = {idx for idx in train_set if idx < embargo_cutoff}
    
    return np.array(sorted(train_set)), np.array(sorted(test_set))


def validate_split_quality(
    data: pd.DataFrame,
    train_indices: np.ndarray,
    test_indices: np.ndarray,
    response_col: str,
    min_test_samples: int,
    min_positive_samples: int,
) -> bool:
    """
    Validate the quality of a train-test split.
    
    Args:
        data: DataFrame with data
        train_indices: Training set indices
        test_indices: Test set indices
        response_col: Name of response column
        min_test_samples: Minimum samples required in test set
        min_positive_samples: Minimum positive samples in test set
        
    Returns:
        True if split meets quality requirements
    """
    # Check minimum test samples
    if len(test_indices) < min_test_samples:
        return False
    
    # Check minimum training samples
    if len(train_indices) < min_test_samples:  # Use same threshold
        return False
    
    # Check positive samples in test set
    test_labels = data.iloc[test_indices][response_col].values
    num_positive_test = np.sum(test_labels.astype(int))
    
    if num_positive_test < min_positive_samples:
        return False
    
    # Check that we have both classes in training set
    train_labels = data.iloc[train_indices][response_col].values
    if len(np.unique(train_labels.astype(int))) < 2:
        return False
    
    return True


def validate_no_data_leakage(
    train_data: pd.DataFrame,
    test_data: pd.DataFrame,
    time_col: str,
    feature_cols: Optional[List[str]] = None,
    strict_temporal: bool = True,
) -> Dict[str, Union[bool, str, List]]:
    """
    Comprehensive data leakage validation.
    
    This function checks for various types of data leakage between training
    and test sets, including temporal leakage and feature leakage.
    
    Args:
        train_data: Training dataset
        test_data: Test dataset
        time_col: Name of time column
        feature_cols: List of feature columns to check (if None, checks all)
        strict_temporal: Whether to enforce strict temporal ordering
        
    Returns:
        Dictionary with validation results
    """
    validation_results = {
        'has_leakage': False,
        'leakage_types': [],
        'details': {},
        'passed_checks': [],
    }
    
    # Check 1: Temporal ordering
    if strict_temporal:
        max_train_time = train_data[time_col].max()
        min_test_time = test_data[time_col].min()
        
        if max_train_time >= min_test_time:
            validation_results['has_leakage'] = True
            validation_results['leakage_types'].append('temporal_overlap')
            validation_results['details']['temporal_overlap'] = {
                'max_train_time': max_train_time,
                'min_test_time': min_test_time,
                'overlap_periods': train_data[train_data[time_col] >= min_test_time][time_col].nunique()
            }
        else:
            validation_results['passed_checks'].append('temporal_ordering')
    
    # Check 2: Duplicate samples
    if feature_cols is None:
        feature_cols = [col for col in train_data.columns if col != time_col]
    
    train_features = train_data[feature_cols]
    test_features = test_data[feature_cols]
    
    # Check for identical feature vectors
    train_hashes = pd.util.hash_pandas_object(train_features)
    test_hashes = pd.util.hash_pandas_object(test_features)
    
    duplicate_hashes = set(train_hashes) & set(test_hashes)
    
    if duplicate_hashes:
        validation_results['has_leakage'] = True
        validation_results['leakage_types'].append('duplicate_samples')
        validation_results['details']['duplicate_samples'] = {
            'num_duplicates': len(duplicate_hashes),
            'duplicate_ratio': len(duplicate_hashes) / len(test_hashes)
        }
    else:
        validation_results['passed_checks'].append('no_duplicate_samples')
    
    # Check 3: Future information in features (basic check)
    # This is a simplified check - in practice, you'd need domain knowledge
    for col in feature_cols:
        if 'future' in col.lower() or 'lead' in col.lower():
            validation_results['has_leakage'] = True
            validation_results['leakage_types'].append('future_information')
            if 'future_information' not in validation_results['details']:
                validation_results['details']['future_information'] = []
            validation_results['details']['future_information'].append(col)
    
    if 'future_information' not in validation_results['leakage_types']:
        validation_results['passed_checks'].append('no_obvious_future_info')
    
    # Check 4: Statistical similarity (basic check)
    # Check if test set statistics are suspiciously similar to training set
    for col in feature_cols:
        if pd.api.types.is_numeric_dtype(train_data[col]):
            train_mean = train_data[col].mean()
            test_mean = test_data[col].mean()
            train_std = train_data[col].std()
            
            if train_std > 0:
                z_score = abs(test_mean - train_mean) / train_std
                if z_score < 0.1:  # Very similar means
                    if 'statistical_similarity' not in validation_results['details']:
                        validation_results['details']['statistical_similarity'] = []
                    validation_results['details']['statistical_similarity'].append({
                        'column': col,
                        'z_score': z_score,
                        'train_mean': train_mean,
                        'test_mean': test_mean
                    })
    
    return validation_results


def prepare_temporal_dataset(
    data: pd.DataFrame,
    time_col: str,
    response_col: str,
    feature_cols: Optional[List[str]] = None,
    label_shift_months: int = 12,
    validate_leakage: bool = True,
) -> Dict[str, Union[pd.DataFrame, List[str], Dict[str, Any]]]:
    """
    Prepare dataset for temporal modeling with proper validation.
    
    Args:
        data: Input DataFrame
        time_col: Name of time column
        response_col: Name of response column
        feature_cols: List of feature columns (if None, infers from data)
        label_shift_months: Number of months to shift labels for prediction
        validate_leakage: Whether to validate for data leakage
        
    Returns:
        Dictionary with prepared data and validation results
    """
    # Sort by time
    data_sorted = data.sort_values(time_col).reset_index(drop=True)
    
    # Infer feature columns if not provided
    if feature_cols is None:
        feature_cols = [col for col in data_sorted.columns 
                       if col not in [time_col, response_col]]
    
    # Apply label shifting if specified
    if label_shift_months > 0:
        data_sorted[response_col] = data_sorted[response_col].shift(-label_shift_months)
        # Remove rows with NaN labels due to shifting
        data_sorted = data_sorted.dropna(subset=[response_col]).reset_index(drop=True)
    
    # Basic validation
    validation_results = {
        'total_samples': len(data_sorted),
        'feature_count': len(feature_cols),
        'positive_ratio': data_sorted[response_col].mean(),
        'time_range': {
            'start': data_sorted[time_col].min(),
            'end': data_sorted[time_col].max(),
            'periods': data_sorted[time_col].nunique()
        }
    }
    
    # Data leakage validation (if requested)
    if validate_leakage:
        # Create a simple train-test split for validation
        split_point = int(len(data_sorted) * 0.8)
        train_subset = data_sorted.iloc[:split_point]
        test_subset = data_sorted.iloc[split_point:]
        
        leakage_check = validate_no_data_leakage(
            train_subset, test_subset, time_col, feature_cols
        )
        validation_results['leakage_validation'] = leakage_check
    
    return {
        'data': data_sorted,
        'feature_columns': feature_cols,
        'validation_results': validation_results,
    }


def create_balanced_splits(
    data: pd.DataFrame,
    response_col: str,
    n_splits: int = 5,
    min_class_ratio: float = 0.1,
) -> List[Tuple[np.ndarray, np.ndarray]]:
    """
    Create balanced train-test splits ensuring minimum class representation.
    
    Args:
        data: DataFrame with data
        response_col: Name of response column
        n_splits: Number of splits to create
        min_class_ratio: Minimum ratio for minority class
        
    Returns:
        List of (train_indices, test_indices) tuples
    """
    labels = data[response_col].values
    positive_indices = np.where(labels == 1)[0]
    negative_indices = np.where(labels == 0)[0]
    
    splits = []
    
    for i in range(n_splits):
        # Calculate sizes for balanced representation
        test_size = len(data) // (n_splits + 1)
        min_positive_test = max(1, int(test_size * min_class_ratio))
        min_negative_test = max(1, int(test_size * (1 - min_class_ratio)))
        
        # Sample test indices
        test_positive = np.random.choice(
            positive_indices, 
            size=min(min_positive_test, len(positive_indices)), 
            replace=False
        )
        test_negative = np.random.choice(
            negative_indices, 
            size=min(min_negative_test, len(negative_indices)), 
            replace=False
        )
        
        test_indices = np.concatenate([test_positive, test_negative])
        train_indices = np.setdiff1d(np.arange(len(data)), test_indices)
        
        splits.append((train_indices, test_indices))
    
    return splits


# Backward compatibility functions
def get_class_weights(labels):
    """Backward compatibility alias for calculate_class_weights."""
    return calculate_class_weights(labels)


def check_data_leakage(train_data, test_data, time_col):
    """Backward compatibility alias for validate_no_data_leakage."""
    return validate_no_data_leakage(train_data, test_data, time_col)

def assert_no_data_leakage_in_preprocessing(
    train_indices: np.ndarray,
    val_indices: np.ndarray,
    test_indices: Optional[np.ndarray] = None,
    preprocessors: Optional[Dict[str, Any]] = None,
    validation_name: str = "validation"
) -> None:
    """
    Comprehensive validation function to ensure no data leakage in preprocessing.
    
    This function performs multiple checks to validate the temporal integrity
    and proper fit-transform pattern in preprocessing pipelines.
    
    Args:
        train_indices: Indices used for training
        val_indices: Indices used for validation
        test_indices: Indices used for testing (optional)
        preprocessors: Dictionary containing fitted preprocessors (optional)
        validation_name: Name of the validation set for error messages
        
    Raises:
        AssertionError: If any data leakage is detected
    """
    print(f"🔍 Validating data leakage prevention for {validation_name} set...")
    
    # 1. Temporal ordering validation
    if len(train_indices) > 0 and len(val_indices) > 0:
        max_train_idx = np.max(train_indices)
        min_val_idx = np.min(val_indices)
        
        if not config.TEMPORAL_CV_ONLY:
            raise AssertionError("TEMPORAL_CV_ONLY must be True to prevent data leakage")
        
        # For temporal CV, validation should come after training (or with proper embargo)
        if min_val_idx <= max_train_idx:
            # Check if there's proper embargo period
            overlap_indices = set(train_indices) & set(val_indices)
            if len(overlap_indices) > 0:
                raise AssertionError(
                    f"Data leakage detected: {len(overlap_indices)} overlapping indices "
                    f"between training and {validation_name} sets"
                )
            
            # Check embargo period
            if min_val_idx > max_train_idx - config.PURGE_SAMPLES:
                print(f"⚠️ Warning: Embargo period may be insufficient. "
                      f"Gap between train and {validation_name}: {min_val_idx - max_train_idx}")
    
    # 2. Test set temporal validation (if provided)
    if test_indices is not None and len(test_indices) > 0:
        if len(train_indices) > 0:
            max_train_idx = np.max(train_indices)
            min_test_idx = np.min(test_indices)
            
            if min_test_idx <= max_train_idx:
                overlap_test = set(train_indices) & set(test_indices)
                if len(overlap_test) > 0:
                    raise AssertionError(
                        f"Data leakage detected: {len(overlap_test)} overlapping indices "
                        f"between training and test sets"
                    )
        
        if len(val_indices) > 0:
            overlap_val_test = set(val_indices) & set(test_indices)
            if len(overlap_val_test) > 0:
                raise AssertionError(
                    f"Data leakage detected: {len(overlap_val_test)} overlapping indices "
                    f"between {validation_name} and test sets"
                )
    
    # 3. Preprocessor validation (if provided)
    if preprocessors is not None:
        required_keys = ['scaler', 'initial_feature_names', 'final_feature_names']
        for key in required_keys:
            if key not in preprocessors:
                print(f"⚠️ Warning: Expected preprocessor key '{key}' not found")
        
        # Check if scaler was fitted (has attributes)
        if 'scaler' in preprocessors and preprocessors['scaler'] is not None:
            scaler = preprocessors['scaler']
            if not hasattr(scaler, 'mean_') or not hasattr(scaler, 'scale_'):
                raise AssertionError("Scaler appears to not be fitted - missing mean_ or scale_ attributes")
            
            print(f"✅ Scaler validation passed - fitted on {len(scaler.mean_)} features")
    
    # 4. Index range validation
    all_indices = np.concatenate([train_indices, val_indices])
    if test_indices is not None:
        all_indices = np.concatenate([all_indices, test_indices])
    
    if len(np.unique(all_indices)) != len(all_indices):
        raise AssertionError("Duplicate indices detected across train/validation/test sets")
    
    # 5. Embargo period validation
    if config.EMBARGO_MONTHS < 3:
        print(f"⚠️ Warning: Embargo period ({config.EMBARGO_MONTHS} months) is less than recommended 3 months")
    
    if config.PURGE_SAMPLES < 90:  # 3 months * 30 days
        print(f"⚠️ Warning: Purge samples ({config.PURGE_SAMPLES}) is less than recommended 90 samples (3 months)")
    
    print(f"✅ Data leakage validation passed for {validation_name} set")
    print(f"   • Train indices: {len(train_indices)} samples (range: {np.min(train_indices) if len(train_indices) > 0 else 'N/A'}-{np.max(train_indices) if len(train_indices) > 0 else 'N/A'})")
    print(f"   • {validation_name.title()} indices: {len(val_indices)} samples (range: {np.min(val_indices) if len(val_indices) > 0 else 'N/A'}-{np.max(val_indices) if len(val_indices) > 0 else 'N/A'})")
    if test_indices is not None:
        print(f"   • Test indices: {len(test_indices)} samples (range: {np.min(test_indices) if len(test_indices) > 0 else 'N/A'}-{np.max(test_indices) if len(test_indices) > 0 else 'N/A'})")

def create_independent_test_split(
    data: pd.DataFrame
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Splits data into a training/validation pool and a final, independent test set.

    The test set is taken from the end of the time series. The function ensures
    the test set is of a minimum size and contains a minimum number of positive samples,
    shifting the split point backward if necessary.

    Returns:
        A tuple of (train_val_pool_df, test_df).
    """
    n_samples = len(data)
    labels = data[config.RESPONSE_COL].to_numpy()
    test_size = int(n_samples * config.TEST_SET_RATIO)
    
    if test_size < config.MIN_TEST_SAMPLES_FOR_SPLIT:
        raise ValueError("Test set size is too small based on current configuration.")

    split_point = n_samples - test_size
    
    # Adjust split point to ensure enough positive samples in test set
    while split_point > 0 and np.sum(labels[split_point:]) < config.MIN_POSITIVE_SAMPLES_IN_TEST:
        split_point -= 1

    if split_point == 0:
        raise ValueError("Could not find a valid split point for the test set with enough positive samples.")
        
    train_val_pool_df = data.iloc[:split_point]
    test_df = data.iloc[split_point:]

    # --- Root-cause fix: Validate that the test set is not empty after splitting ---
    if test_df.empty:
        raise ValueError("Independent test set is empty after splitting. Check data size, label distribution, and configuration.")

    # --- Root-cause fix: Validate that enough features remain after preprocessing ---
    # This check should be performed after preprocessing, but we add a placeholder here for clarity.
    # After preprocessing, if test_df has too few features (e.g., <10), raise an error.
    # (The actual feature count check should be done after preprocessing in the pipeline.)

    # Validate temporal split
    train_val_indices = np.arange(len(train_val_pool_df))
    test_indices = np.arange(split_point, n_samples)
    
    if config.VALIDATE_NO_LEAKAGE:
        assert_no_data_leakage_in_preprocessing(
            train_indices=train_val_indices,
            val_indices=np.array([]),  # No validation set at this stage
            test_indices=test_indices,
            validation_name="independent_test"
        )
    
    print(f"Created independent test split: Train Pool={len(train_val_pool_df)}, Test Set={len(test_df)}")
    return train_val_pool_df, test_df

def create_purged_k_folds(
    data: pd.DataFrame, n_splits: int
) -> List[Tuple[np.ndarray, np.ndarray]]:
    """
    ENHANCED BiLSTM-Style Temporal Cross-Validation with Guaranteed Positive Samples
    ============================================================================
    
    CRITICAL FIXES:
    1. Guaranteed minimum positive samples in each fold using stratified temporal sampling
    2. Intelligent fold size adjustment based on positive sample distribution
    3. Robust fallback strategies for extreme class imbalance
    4. Never skip folds - always return n_splits valid folds
    
    Temporal Structure:
    Fold 1: [Train: earliest data] → [Embargo] → [Val: window 1] → [Future]
    Fold 2: [Train: earliest + more] → [Embargo] → [Val: window 2] → [Future]
    ...
    
    Args:
        data (pd.DataFrame): The training/validation data pool (chronologically sorted).
        n_splits (int): Number of temporal folds to create.
        
    Returns:
        List of (train_indices, validation_indices) tuples with guaranteed positive samples
    """
    print(f"🔄 [ENHANCED Temporal CV] Creating {n_splits} folds with GUARANTEED positive samples...")
    
    # Sort data by time for strict chronological splits
    data_sorted = data.sort_values(config.TIME_COL).reset_index(drop=True)
    n_samples = len(data_sorted)
    labels = data_sorted[config.RESPONSE_COL].to_numpy()
    
    # CRITICAL: Analyze positive sample distribution
    positive_indices = np.where(labels == 1)[0]
    total_positives = len(positive_indices)
    positive_ratio = total_positives / n_samples
    
    print(f"📊 Data Analysis:")
    print(f"   • Total samples: {n_samples}")
    print(f"   • Positive samples: {total_positives} ({positive_ratio:.2%})")
    print(f"   • Negative samples: {n_samples - total_positives}")
    
    # IMPROVED: More realistic positive sample allocation
    # Calculate how many positive samples we can realistically expect per fold
    min_positives_per_fold = max(1, total_positives // (n_splits * 3))  # More conservative distribution
    min_positives_per_fold = min(min_positives_per_fold, 2)  # Cap at 2 for better feasibility

    print(f"   • Target positive samples per fold: {min_positives_per_fold}")

    # IMPROVED: Analyze temporal distribution of positive samples
    # Check if positive samples are evenly distributed across time
    temporal_thirds = n_samples // 3
    early_positives = np.sum(labels[:temporal_thirds])
    middle_positives = np.sum(labels[temporal_thirds:2*temporal_thirds])
    late_positives = np.sum(labels[2*temporal_thirds:])

    print(f"   • Temporal distribution: Early={early_positives}, Middle={middle_positives}, Late={late_positives}")

    # IMPROVED: Adaptive positive sample allocation based on temporal distribution
    # If positive samples are concentrated in early periods, adjust expectations for later folds
    if late_positives < total_positives * 0.2:  # Less than 20% of positives in last third
        print(f"   ⚠️ Positive samples concentrated in early periods - adjusting expectations")
        # Use a declining allocation for later folds
        positive_allocation = []
        remaining_positives = total_positives
        for i in range(n_splits):
            # Allocate more positives to earlier folds
            fold_weight = (n_splits - i) / sum(range(1, n_splits + 1))
            fold_positives = max(1, int(remaining_positives * fold_weight))
            fold_positives = min(fold_positives, remaining_positives - (n_splits - i - 1))  # Ensure we don't run out
            positive_allocation.append(fold_positives)
            remaining_positives -= fold_positives
    else:
        # Standard equal distribution
        positive_samples_per_fold = total_positives // n_splits
        extra_positive_samples = total_positives % n_splits
        positive_allocation = []
        for i in range(n_splits):
            fold_positives = positive_samples_per_fold + (1 if i < extra_positive_samples else 0)
            positive_allocation.append(fold_positives)
    
    # Calculate validation window sizes with positive sample guarantees
    base_val_window_size = max(50, n_samples // (n_splits * 2))  # Conservative window sizing
    
    # Ensure we have enough space for training growth
    max_val_space = n_samples - 200  # Reserve 200 samples for training
    total_val_space = min(max_val_space, n_splits * base_val_window_size)
    val_window_size = max(base_val_window_size, total_val_space // n_splits)
    
    print(f"📊 Fold Configuration:")
    print(f"   • Validation window size: {val_window_size}")
    print(f"   • Positive allocation per fold: {positive_allocation}")

    # ENHANCED: Create folds with realistic positive sample expectations
    split_results = []
    val_start_offset = n_samples - (n_splits * val_window_size) - config.PURGE_SAMPLES
    val_start_offset = max(100, val_start_offset)  # Ensure minimum training data
    
    # ENHANCED: Stratified fold creation
    for i in range(n_splits):
        print(f"\n   🎯 Creating Fold {i+1}/{n_splits}...")
        
        # Calculate target positive samples for this fold
        target_positives = positive_allocation[i]
        
        # Find validation window that contains required positive samples
        best_val_start = val_start_offset + (i * val_window_size)
        best_val_end = min(best_val_start + val_window_size, n_samples)
        
        # ENHANCED: Search for optimal validation window with positive samples
        search_attempts = 0
        max_search_attempts = 20
        found_sufficient_positives = False
        
        while search_attempts < max_search_attempts and not found_sufficient_positives:
            # Count positive samples in current window
            current_positives = np.sum(labels[best_val_start:best_val_end])
            
            if current_positives >= target_positives:
                found_sufficient_positives = True
                break
            
            # Adjust window position to find more positive samples
            if search_attempts < max_search_attempts // 2:
                # Try moving window forward
                new_start = min(best_val_start + 10, n_samples - val_window_size)
                best_val_start = new_start
                best_val_end = min(best_val_start + val_window_size, n_samples)
            else:
                # Try expanding window size
                expansion = min(20, n_samples - best_val_end)
                best_val_end = min(best_val_end + expansion, n_samples)
            
            search_attempts += 1
        
        # IMPROVED: More flexible handling when target positives not found
        if not found_sufficient_positives:
            current_positives = np.sum(labels[best_val_start:best_val_end])

            # Check if we have at least 1 positive sample (minimum requirement)
            if current_positives >= 1:
                print(f"   ✅ Found {current_positives} positive samples (target was {target_positives})")
                val_start = best_val_start
                val_end = best_val_end
            else:
                # Try to find ANY positive samples in the temporal vicinity
                vicinity_start = max(0, best_val_start - 100)
                vicinity_end = min(n_samples, best_val_end + 100)
                vicinity_positives = positive_indices[
                    (positive_indices >= vicinity_start) & (positive_indices < vicinity_end)
                ]

                if len(vicinity_positives) > 0:
                    # Extend window to include at least one positive sample
                    closest_positive = vicinity_positives[0]  # Take the first available
                    val_start = min(best_val_start, closest_positive)
                    val_end = max(best_val_end, closest_positive + 1)

                    # Keep window size reasonable
                    if val_end - val_start > val_window_size * 1.5:
                        val_start = max(0, closest_positive - val_window_size // 2)
                        val_end = min(n_samples, val_start + val_window_size)

                    print(f"   ✅ Extended window to include positive sample at index {closest_positive}")
                else:
                    # No positive samples available - use original window
                    val_start = best_val_start
                    val_end = best_val_end
                    print(f"   ⚠️ No positive samples available in temporal vicinity - proceeding with validation window")
        else:
            val_start = best_val_start
            val_end = best_val_end
            print(f"   ✅ Found window {val_start}-{val_end} with {np.sum(labels[val_start:val_end])} positives")
        
        # Create validation indices
        val_indices = np.arange(val_start, val_end)
        
        # ENHANCED: Create training indices with proper embargo
        train_end = val_start - config.PURGE_SAMPLES
        train_end = max(50, train_end)  # Ensure minimum training data
        
        if train_end <= 0:
            print(f"   ⚠️ Adjusting training range due to embargo constraints")
            train_end = 50
            val_start = train_end + config.PURGE_SAMPLES
            val_end = min(val_start + val_window_size, n_samples)
            val_indices = np.arange(val_start, val_end)
        
        train_indices = np.arange(0, train_end)
        
        # ENHANCED: Validate fold quality
        train_positives = np.sum(labels[train_indices])
        val_positives = np.sum(labels[val_indices])
        
        # ENHANCED: Apply stratified sampling if needed
        if val_positives < min_positives_per_fold and len(positive_indices) > 0:
            print(f"   🔧 Applying stratified sampling to guarantee {min_positives_per_fold} positive samples")
            
            # Find available positive samples not in training set
            available_positives = positive_indices[positive_indices >= train_end]
            
            if len(available_positives) >= min_positives_per_fold:
                # Select required positive samples
                selected_positives = np.random.choice(available_positives, min_positives_per_fold, replace=False)
                
                # Add corresponding negative samples to maintain balance
                negative_indices_available = np.where(labels == 0)[0]
                negative_indices_available = negative_indices_available[negative_indices_available >= train_end]
                
                if len(negative_indices_available) > 0:
                    # Calculate how many negative samples to add
                    target_negatives = min(len(negative_indices_available), len(val_indices) - min_positives_per_fold)
                    selected_negatives = np.random.choice(negative_indices_available, target_negatives, replace=False)
                    
                    # Create new validation indices
                    new_val_indices = np.concatenate([selected_positives, selected_negatives])
                    val_indices = np.sort(new_val_indices)
                    
                    val_positives = np.sum(labels[val_indices])
                    print(f"   ✅ Stratified sampling result: {val_positives} positive samples")
        
        # ENHANCED: Final validation and reporting
        train_positives = np.sum(labels[train_indices])
        val_positives = np.sum(labels[val_indices])
        
        # Temporal integrity check
        max_train_idx = np.max(train_indices)
        min_val_idx = np.min(val_indices)
        embargo_gap = min_val_idx - max_train_idx - 1
        
        if embargo_gap < config.PURGE_SAMPLES:
            print(f"   ⚠️ Embargo gap ({embargo_gap}) less than required ({config.PURGE_SAMPLES})")
        
        # Report fold statistics
        print(f"   📊 Fold {i+1} Statistics:")
        print(f"      • Training: {len(train_indices)} samples, {train_positives} positives ({train_positives/len(train_indices)*100:.1f}%)")
        print(f"      • Validation: {len(val_indices)} samples, {val_positives} positives ({val_positives/len(val_indices)*100:.1f}%)")
        print(f"      • Embargo gap: {embargo_gap} samples")
        print(f"      • Temporal ordering: {'✅' if max_train_idx < min_val_idx else '❌'}")
        
        # ENHANCED: Never skip folds - always add to results
        split_results.append((train_indices, val_indices))
    
    # ENHANCED: Final validation
    if len(split_results) == 0:
        raise ValueError("No valid temporal folds could be created")
    
    if len(split_results) < n_splits:
        print(f"⚠️ Created {len(split_results)} folds instead of requested {n_splits}")
    
    # Report final statistics
    print(f"\n✅ [ENHANCED Temporal CV] Successfully created {len(split_results)} folds")
    print(f"📊 SUMMARY:")
    
    training_sizes = []
    validation_sizes = []
    val_positive_counts = []
    
    for i, (train_idx, val_idx) in enumerate(split_results):
        train_size = len(train_idx)
        val_size = len(val_idx)
        val_pos = np.sum(labels[val_idx])
        
        training_sizes.append(train_size)
        validation_sizes.append(val_size)
        val_positive_counts.append(val_pos)
        
        print(f"   • Fold {i+1}: Train={train_size}, Val={val_size}, Val_pos={val_pos}")
    
    print(f"   • Training sizes: {training_sizes[0]} → {training_sizes[-1]} (expanding: {'✅' if all(training_sizes[i] <= training_sizes[i+1] for i in range(len(training_sizes)-1)) else '❌'})")
    print(f"   • Validation positive samples: {val_positive_counts} (min: {min(val_positive_counts)}, max: {max(val_positive_counts)})")
    print(f"   • Guaranteed positive samples: {'✅' if min(val_positive_counts) > 0 else '❌'}")
    
    return split_results