#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Financial Graph Builder Package

A comprehensive toolkit for building financial networks from time series data
using lagged correlation analysis and causal relationship detection.

This package provides tools for:
- Financial time series preprocessing with data cleaning and imputation.
- Three diverse correlation metrics (<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>) with 
  bidirectional lagged analysis to detect causal relationships.
- Robust, rank-based aggregation of correlation metrics.
- Precise graph construction using a Top-K Causal In-Degree strategy.
"""

__version__ = "1.1.0"
__author__ = "Financial Graph Builder Team"
__description__ = "Financial network construction with lagged correlation analysis"

# Import key modules
from . import config
from . import data_preprocessing
from . import correlation_metrics
from . import graph_builder

# Import key functions for easy access
from .data_preprocessing import (
    fit_preprocessors,
    apply_preprocessors,
    load_raw_data
)
from .correlation_metrics import build_correlation_matrices
from .graph_builder import (
    build_graphs_for_window, 
    aggregate_correlation_metrics,
)

# Define what gets imported with "from financial_graph_builder import *"
__all__ = [
    # Modules
    "config",
    "data_preprocessing", 
    "correlation_metrics",
    "graph_builder",
    
    # Key functions
    "fit_preprocessors",
    "apply_preprocessors",
    "load_raw_data",
    "build_correlation_matrices",
    "build_graphs_for_window",
    "aggregate_correlation_metrics", 
]

def get_version():
    """Return the package version."""
    return __version__

def print_package_info():
    """Print package information and capabilities."""
    print("=" * 80)
    print(f"Financial Graph Builder v{__version__}")
    print("=" * 80)
    print(__description__)
    print()
    print("Key Features:")
    print("• Advanced financial time series preprocessing")
    print("• Three diverse correlation metrics with lagged analysis")
    print("• Bidirectional causality detection") 
    print("• Rank-based metric aggregation")
    print("• Directed (Top-K Causal In-Degree) and undirected graph construction")
    print("• Fully integrated with a purged k-fold CV pipeline")
    print()
    print("Configuration:")
    print(f"• Data Directory: {config.DATA_DIR}")
    print(f"• Max Lag: {config.MAX_LAG}")
    print(f"• Directed Graph: {config.DIRECTED_GRAPH}")
    print(f"• Causality Threshold: {config.CAUSALITY_THRESHOLD}")
    print("=" * 80)

def validate_package_setup():
    """Validate that the package is properly configured."""
    config.validate_config()
    print("✅ Package configuration is valid")
    return True 