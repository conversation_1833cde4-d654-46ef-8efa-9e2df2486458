#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration module for Financial Graph Builder.

This module contains core configuration parameters for data preprocessing
and dynamic graph construction.

Note: Many preprocessing parameters are now centralized in the main 
gnn_lstm.config module. This file contains fallback values and 
graph-specific configurations.
"""

import os

# ──────────────────────────────────────────────────────────────────────────────
# 1) File Paths and Directories
# ──────────────────────────────────────────────────────────────────────────────
# Note: These are fallback paths. The main pipeline uses paths from the root config.
DATA_DIR = r"D:/Documents/papers/GNN预测金融危机/data/monthly_data"
RAW_CSV = os.path.join(DATA_DIR, "US_full_data_monthly_withNA.csv")
PREPROC_DIR = os.path.join(DATA_DIR, "preprocessed_data")
os.makedirs(PREPROC_DIR, exist_ok=True)

# ──────────────────────────────────────────────────────────────────────────────
# 2) Data Preprocessing Parameters (Fallback Values)
# ──────────────────────────────────────────────────────────────────────────────
# NOTE: These parameters are now centralized in gnn_lstm.config
# The values here serve as fallbacks for standalone usage of this module
RESPONSE_COL = "Response"
TIME_COL = "TIME_PERIOD"
MAX_NA_RATIO = 0.2
RANDOM_STATE = 42

# ──────────────────────────────────────────────────────────────────────────────
# 3) Graph Construction Parameters
# ──────────────────────────────────────────────────────────────────────────────
# These parameters are now primarily set and optimized via the main pipeline's HPO.
# The values here serve as defaults or fallbacks.
DIRECTED_GRAPH = True
WEIGHTED_EDGES = True
MAX_LAG = 3
CAUSALITY_THRESHOLD = 0.3
TOP_K = 8
LAG_DECAY = 0.8
INCLUDE_CONTEMPORANEOUS = True
BIDIRECTIONAL_ALLOWED = False
ADJUST_NODE_FEATURES = True
RETAIN_FULL_WINDOW = False

# Metric-specific max lags provide a more nuanced approach to correlation analysis.
USE_METRIC_SPECIFIC_LAGS = True
METRIC_SPECIFIC_MAX_LAGS = {
    'pearson': 2,
    'spearman': 3,
    'chatterjee': 4
}

# ──────────────────────────────────────────────────────────────────────────────
# 4) Processing Settings
# ──────────────────────────────────────────────────────────────────────────────
CPU_COUNT = os.cpu_count()
# Force n_jobs=1 on Windows to prevent memory errors with joblib and PyTorch.
# The error "OSError: [WinError 1455] The paging file is too small" occurs
# when multiple processes try to load torch CUDA libraries simultaneously.
# Running in a single process is slower but more stable on memory-constrained systems.
N_JOBS = 1

# Fallback imputation method (main config takes precedence)
IMPUTATION_METHOD = 'ffill_bfill'

def print_config():
    """Print current configuration settings."""
    print("=== Financial Graph Builder Configuration ===")
    print(f"Data Directory: {DATA_DIR}")
    print(f"Raw CSV: {RAW_CSV}")
    print(f"Preprocessed Directory: {PREPROC_DIR}")
    print(f"Metric-Specific Max Lags: {METRIC_SPECIFIC_MAX_LAGS}")
    print(f"Use Metric-Specific Lags: {USE_METRIC_SPECIFIC_LAGS}")
    print(f"Directed Graph: {DIRECTED_GRAPH}")
    print(f"Causality Threshold: {CAUSALITY_THRESHOLD}")
    print(f"Top-K Connections: {TOP_K}")
    print(f"Max Lag: {MAX_LAG}")
    print(f"Imputation Method: {IMPUTATION_METHOD}")
    print("\nNOTE: Main preprocessing parameters are centralized in gnn_lstm.config")
    print("=" * 45)

def validate_config():
    """Validate configuration parameters and file paths."""
    errors = []
    
    # Check if raw CSV exists
    if not os.path.exists(RAW_CSV):
        errors.append(f"Raw CSV file not found: {RAW_CSV}")
    
    # Validate numeric parameters
    if MAX_LAG < 0:
        errors.append("MAX_LAG must be >= 0")
    
    if CAUSALITY_THRESHOLD < 0 or CAUSALITY_THRESHOLD > 1:
        errors.append("CAUSALITY_THRESHOLD must be between 0 and 1")
    
    if TOP_K <= 0:
        errors.append("TOP_K must be > 0")
    
    if errors:
        raise ValueError(f"Configuration validation failed:\n" + "\n".join(errors))
    
    print("✅ Configuration validation passed")

def get_main_config_note():
    """Return a note about centralized configuration."""
    return (
        "Most preprocessing parameters (MICE_RIDGE_ALPHA, MICE_RANDOM_STATE, "
        "MICE_MAX_ITER, CONSTANT_FEATURE_THRESHOLD, etc.) are now centralized "
        "in gnn_lstm.config for consistency across the entire pipeline."
    ) 