#!/usr/bin/env python3
"""
Ultra-Enhanced TSAP Performance Test

Test the comprehensive improvements made to address the performance gap:
- TSAP F0.5: 0.5769 → Target: 0.75+ (competing with Plain-GNN's 0.8333)
- Minimal compression ratio (1.3:1 vs previous 2:1)
- Advanced pooling techniques (residual + multi-scale)
- Ultra-optimized training parameters
"""

import sys
import os
import torch
import numpy as np
from torch_geometric.data import Data, Batch

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gnn_lstm import config
from gnn_lstm.models import TSAPGnnLSTM, PlainGNN

def test_ultra_enhanced_architecture():
    """Test the ultra-enhanced TSAP architecture improvements."""
    print("🚀" + "=" * 80)
    print("🎯 ULTRA-ENHANCED TSAP ARCHITECTURE TEST")
    print("🚀" + "=" * 80)
    
    print(f"\n📊 BREAKTHROUGH IMPROVEMENTS:")
    print(f"   • Clusters: {config.TSAP_NUM_CLUSTERS} (compression: {118/config.TSAP_NUM_CLUSTERS:.1f}:1)")
    print(f"   • Spectral Components: {config.TSAP_NUM_SPECTRAL_COMPONENTS}")
    print(f"   • Adjacency Threshold: {config.TSAP_ADJACENCY_THRESHOLD}")
    print(f"   • Residual Pooling: {config.TSAP_USE_RESIDUAL_POOLING}")
    print(f"   • Multi-Scale Pooling: {config.TSAP_USE_MULTI_SCALE_POOLING}")
    
    try:
        # Test ultra-enhanced TSAP model
        model = TSAPGnnLSTM(
            node_feature_dim=9,
            **config.TSAP_CONFIG
        )
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n✅ Ultra-Enhanced TSAP Model Created Successfully")
        print(f"   • Total Parameters: {total_params:,}")
        print(f"   • Trainable Parameters: {trainable_params:,}")
        print(f"   • Model Complexity: {'High' if total_params > 100000 else 'Moderate'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """Compare ultra-enhanced TSAP vs Plain-GNN on synthetic data."""
    print(f"\n🧪 PERFORMANCE COMPARISON TEST")
    print("=" * 60)
    
    try:
        # Create test data
        batch_size = 4
        num_nodes_per_graph = 118
        node_feature_dim = 9
        
        # Create sample graphs
        graphs = []
        for _ in range(batch_size):
            x = torch.randn(num_nodes_per_graph, node_feature_dim)
            num_edges = 80  # Increased connectivity
            edge_index = torch.randint(0, num_nodes_per_graph, (2, num_edges))
            graph = Data(x=x, edge_index=edge_index)
            graphs.append(graph)
        
        batch = Batch.from_data_list(graphs)
        
        # Initialize models
        tsap_model = TSAPGnnLSTM(node_feature_dim=node_feature_dim, **config.TSAP_CONFIG)
        plain_model = PlainGNN(node_feature_dim=node_feature_dim, **config.PLAIN_GNN_CONFIG)
        
        # Forward pass comparison
        tsap_model.eval()
        plain_model.eval()
        
        with torch.no_grad():
            tsap_output = tsap_model(batch)
            plain_output = plain_model(batch)
        
        print(f"✅ Forward Pass Comparison:")
        print(f"   • TSAP Output Shape: {tsap_output.shape}")
        print(f"   • Plain Output Shape: {plain_output.shape}")
        print(f"   • TSAP Output Range: [{tsap_output.min():.4f}, {tsap_output.max():.4f}]")
        print(f"   • Plain Output Range: [{plain_output.min():.4f}, {plain_output.max():.4f}]")
        
        # Test TSAP pooling effectiveness
        tsap_pooled, _, aux_features = tsap_model.tsap(batch.x, batch.edge_index, batch.batch)  # type: ignore
        compression_ratio = batch.x.shape[0] / tsap_pooled.shape[0]  # type: ignore
        
        print(f"\n📊 TSAP Pooling Analysis:")
        print(f"   • Input Nodes: {batch.x.shape[0]}")  # type: ignore
        print(f"   • Pooled Nodes: {tsap_pooled.shape[0]}")
        print(f"   • Compression Ratio: {compression_ratio:.1f}:1")
        print(f"   • Information Preservation: {(1/compression_ratio)*100:.1f}%")
        print(f"   • Auxiliary Features Shape: {aux_features.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_configuration():
    """Test the ultra-optimized training configuration."""
    print(f"\n⚙️ ULTRA-OPTIMIZED TRAINING CONFIGURATION")
    print("=" * 60)
    
    print(f"📈 Training Parameters:")
    print(f"   • Max Epochs: {config.MAX_TRAINING_EPOCHS}")
    print(f"   • Patience: {config.EXTENDED_PATIENCE}")
    print(f"   • Min Epochs: {config.MIN_EPOCHS_BEFORE_STOPPING}")
    print(f"   • Learning Rate: {config.LEARNING_RATE}")
    print(f"   • Weight Decay: {config.WEIGHT_DECAY}")
    print(f"   • Batch Size: {config.BATCH_SIZE}")
    
    print(f"\n🎯 Expected Performance Targets:")
    print(f"   • Current TSAP F0.5: 0.5769")
    print(f"   • Target TSAP F0.5: 0.75+")
    print(f"   • Plain-GNN F0.5: 0.8333")
    print(f"   • Performance Gap to Close: {0.8333 - 0.5769:.4f}")
    
    return True

def main():
    """Run all ultra-enhanced TSAP tests."""
    print("🎯" + "=" * 80)
    print("🚀 ULTRA-ENHANCED TSAP COMPREHENSIVE TESTING")
    print("🎯" + "=" * 80)
    
    tests = [
        ("Ultra-Enhanced Architecture", test_ultra_enhanced_architecture),
        ("Performance Comparison", test_performance_comparison),
        ("Training Configuration", test_training_configuration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*80}")
        print(f"🧪 {test_name.upper()} TEST")
        print(f"{'='*80}")
        
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print(f"\n{'='*80}")
    print(f"📋 COMPREHENSIVE TEST SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {test_name:<30} {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"✅ All tests passed! Ultra-Enhanced TSAP is ready for training.")
        print(f"🚀 Expected performance improvement: 0.5769 → 0.75+ F0.5")
        print(f"💡 Run training pipeline to validate performance gains.")
    else:
        print(f"⚠️ Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    main()
