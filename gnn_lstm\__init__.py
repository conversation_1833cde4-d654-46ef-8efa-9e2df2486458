"""
GNN-LSTM Financial Crisis Prediction System

A comprehensive implementation of Graph Neural Networks with LSTM for financial crisis prediction,
featuring Temporal Spectral Attention Pooling (TSAP) architecture.

Key Components:
- TSAP GNN-LSTM: Advanced model with temporal spectral attention pooling
- Plain GNN: Baseline model for comparison
- Financial Graph Builder: Dynamic graph construction from financial data
- Enhanced Training: Comprehensive training utilities with early stopping
- Evaluation: F0.5-optimized evaluation metrics

Usage:
    from gnn_lstm import config, models, training_utils, evaluation

    # Initialize model
    model = models.TSAPGnnLSTM(node_feature_dim=9, **config.TSAP_CONFIG)

    # Train model
    results = training_utils.train_model(model, train_loader, val_loader)
"""

__version__ = "1.0.0"
__author__ = "GNN-LSTM Research Team"

# Import main components for easy access
from . import config
from . import data_utils
from . import evaluation
from . import training_utils
from . import models
from . import financial_graph_builder

# Import key classes for direct access
from .models import TSAPGnnLSTM, PlainGNN, TemporalSpectralAttentionPooling

__all__ = [
    "config",
    "data_utils",
    "evaluation",
    "training_utils",
    "models",
    "financial_graph_builder",
    "TSAPGnnLSTM",
    "PlainGNN",
    "TemporalSpectralAttentionPooling"
]