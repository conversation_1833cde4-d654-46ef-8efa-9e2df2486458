#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Correlation metrics module for Financial Graph Builder.

This module implements three diverse correlation metrics with bidirectional
lagged analysis for detecting causal relationships in financial time series.
"""

import numpy as np
from scipy.stats import rankdata
from typing import Callable, Dict, Optional
from joblib import Parallel, delayed

from . import config


def compute_bidirectional_lagged_correlation(x: np.ndarray, y: np.ndarray, 
                                           max_lag: int, metric_fn: Callable[[np.ndarray, np.ndarray], float]) -> dict:
    """
    Compute correlations with lags in both directions to determine causality.
    Enhanced with comprehensive validation to prevent NaN/empty slice warnings.
    """
    # Comprehensive input validation
    if x is None or y is None:
        return _get_default_correlation_result()
    
    x = np.asarray(x, dtype=np.float64)
    y = np.asarray(y, dtype=np.float64)
    
    # Check for valid input shapes and data
    if x.size == 0 or y.size == 0:
        print(f"⚠️ Warning: Empty input arrays (x.size={x.size}, y.size={y.size})")
        return _get_default_correlation_result()
    
    if len(x) != len(y):
        print(f"⚠️ Warning: Mismatched array lengths (x={len(x)}, y={len(y)})")
        return _get_default_correlation_result()
    
    n = len(x)
    if n < 3:  # Need minimum samples for meaningful correlation
        print(f"⚠️ Warning: Insufficient samples (n={n}) for correlation calculation")
        return _get_default_correlation_result()
    
    # Check for NaN or infinite values
    if np.any(~np.isfinite(x)) or np.any(~np.isfinite(y)):
        print(f"⚠️ Warning: Non-finite values detected in correlation inputs")
        # Remove non-finite values
        valid_mask = np.isfinite(x) & np.isfinite(y)
        if np.sum(valid_mask) < 3:
            print(f"⚠️ Warning: Too few finite values after cleaning ({np.sum(valid_mask)})")
            return _get_default_correlation_result()
        x = x[valid_mask]
        y = y[valid_mask]
        n = len(x)
    
    # Check for zero variance (constant arrays) - silently handle
    x_var = np.var(x)
    y_var = np.var(y)
    if x_var < 1e-12 or y_var < 1e-12:
        # Return default result silently - preprocessing should prevent this
        return _get_default_correlation_result()
    
    x_leads_y_results, y_leads_x_results = {}, {}
    
    # Contemporaneous correlation with validation
    corr_lag_0 = _safe_metric_computation(x, y, metric_fn)
    x_leads_y_results[0], y_leads_x_results[0] = corr_lag_0, corr_lag_0
    
    # Lagged correlations with enhanced validation
    for lag in range(1, max_lag + 1):
        if n - lag < 3: 
            continue
            
        # X leads Y: use x[:-lag] and y[lag:]
        x_subset = x[:-lag]
        y_subset = y[lag:]
        
        # Validate subsets before computation
        if _validate_correlation_inputs(x_subset, y_subset):
            x_leads_y_results[lag] = _safe_metric_computation(x_subset, y_subset, metric_fn)
        else:
            x_leads_y_results[lag] = 0.0
        
        # Y leads X: use x[lag:] and y[:-lag]
        x_subset = x[lag:]
        y_subset = y[:-lag]
        
        if _validate_correlation_inputs(x_subset, y_subset):
            y_leads_x_results[lag] = _safe_metric_computation(x_subset, y_subset, metric_fn)
        else:
            y_leads_x_results[lag] = 0.0

    # Find best lag and correlation for each direction (with fallback)
    if x_leads_y_results:
        best_x_leads_lag, best_x_leads_corr = max(x_leads_y_results.items(), key=lambda item: abs(item[1]))
    else:
        best_x_leads_lag, best_x_leads_corr = 0, 0.0
        
    if y_leads_x_results:
        best_y_leads_lag, best_y_leads_corr = max(y_leads_x_results.items(), key=lambda item: abs(item[1]))
    else:
        best_y_leads_lag, best_y_leads_corr = 0, 0.0

    # Determine causality: use relative difference for more robust detection
    abs_x_leads, abs_y_leads = abs(best_x_leads_corr), abs(best_y_leads_corr)
    causality = 0
    if abs_x_leads > abs_y_leads * 1.1: causality = 1  # x -> y
    elif abs_y_leads > abs_x_leads * 1.1: causality = -1 # y -> x
        
    significant_causality = (
        (causality != 0 and max(abs_x_leads, abs_y_leads) > config.CAUSALITY_THRESHOLD) or
        (causality == 0 and max(abs_x_leads, abs_y_leads) > config.CAUSALITY_THRESHOLD and config.BIDIRECTIONAL_ALLOWED)
    )
    
    return {
        'x_leads_y': (best_x_leads_lag, best_x_leads_corr),
        'y_leads_x': (best_y_leads_lag, best_y_leads_corr),
        'causality': causality if significant_causality else 0,
        'causality_strength': abs(abs_x_leads - abs_y_leads),
    }


def _get_default_correlation_result() -> dict:
    """Return default correlation result for invalid inputs."""
    return {
        'x_leads_y': (0, 0.0),
        'y_leads_x': (0, 0.0),
        'causality': 0,
        'causality_strength': 0.0,
    }


def _validate_correlation_inputs(x: np.ndarray, y: np.ndarray) -> bool:
    """Validate inputs for correlation computation."""
    if x.size == 0 or y.size == 0:
        return False
    if len(x) != len(y):
        return False
    if len(x) < 3:
        return False
    if np.any(~np.isfinite(x)) or np.any(~np.isfinite(y)):
        return False
    if np.var(x) < 1e-12 or np.var(y) < 1e-12:
        return False
    return True


def _safe_metric_computation(x: np.ndarray, y: np.ndarray, metric_fn: Callable[[np.ndarray, np.ndarray], float]) -> float:
    """Safely compute correlation metric with comprehensive error handling."""
    if not _validate_correlation_inputs(x, y):
        return 0.0
    
    # Additional validation for metric computation
    result = metric_fn(x, y)
    
    # Validate result
    if not np.isfinite(result):
        print(f"⚠️ Warning: Non-finite correlation result: {result}")
        return 0.0
    
    # Clip to valid correlation range
    result = np.clip(result, -1.0, 1.0)
    
    return float(result)


def bidirectional_lagged_pearson(x: np.ndarray, y: np.ndarray, max_lag: int) -> dict:
    """Computes lagged Pearson correlation with enhanced validation."""
    def pearson_fn(a: np.ndarray, b: np.ndarray) -> float:
        # Enhanced validation before Pearson computation
        if not _validate_correlation_inputs(a, b):
            return 0.0
            
        # Remove any remaining non-finite values
        valid_mask = np.isfinite(a) & np.isfinite(b)
        if np.sum(valid_mask) < 3:
            return 0.0
            
        a_clean = a[valid_mask]
        b_clean = b[valid_mask]
        
        # Check variance again after cleaning
        if np.var(a_clean) < 1e-12 or np.var(b_clean) < 1e-12:
            return 0.0
        
        # Compute correlation with error handling
        corr_matrix = np.corrcoef(a_clean, b_clean)
        if corr_matrix.shape != (2, 2):
            return 0.0
            
        corr = corr_matrix[0, 1]
        return 0.0 if np.isnan(corr) else float(np.clip(corr, -1.0, 1.0))
        
    return compute_bidirectional_lagged_correlation(x, y, max_lag, pearson_fn)


def bidirectional_lagged_spearman(x: np.ndarray, y: np.ndarray, max_lag: int) -> dict:
    """Computes lagged Spearman correlation with enhanced validation."""
    def spearman_fn(a: np.ndarray, b: np.ndarray) -> float:
        if not _validate_correlation_inputs(a, b):
            return 0.0
            
        # Remove any remaining non-finite values
        valid_mask = np.isfinite(a) & np.isfinite(b)
        if np.sum(valid_mask) < 3:
            return 0.0
            
        a_clean = a[valid_mask]
        b_clean = b[valid_mask]
        
        # Check for unique values (required for ranking)
        if len(np.unique(a_clean)) < 2 or len(np.unique(b_clean)) < 2:
            return 0.0
        
        # Compute ranks safely
        a_rank = rankdata(a_clean, method='average')
        b_rank = rankdata(b_clean, method='average')
        
        # Check rank variance
        if np.var(a_rank) < 1e-12 or np.var(b_rank) < 1e-12:
            return 0.0
        
        corr_matrix = np.corrcoef(a_rank, b_rank)
        if corr_matrix.shape != (2, 2):
            return 0.0
            
        corr = corr_matrix[0, 1]
        return 0.0 if np.isnan(corr) else float(np.clip(corr, -1.0, 1.0))
        
    return compute_bidirectional_lagged_correlation(x, y, max_lag, spearman_fn)


def bidirectional_lagged_chatterjee(x: np.ndarray, y: np.ndarray, max_lag: int) -> dict:
    """Computes lagged Chatterjee's ξ correlation with enhanced validation."""
    def chatterjee_fn(a: np.ndarray, b: np.ndarray) -> float:
        if not _validate_correlation_inputs(a, b):
            return 0.0
            
        # Remove any remaining non-finite values
        valid_mask = np.isfinite(a) & np.isfinite(b)
        if np.sum(valid_mask) < 3:
            return 0.0
            
        a_clean = a[valid_mask]
        b_clean = b[valid_mask]
        n = len(a_clean)
        
        if n < 2:
            return 0.0
            
        # Check for sufficient variation
        if len(np.unique(a_clean)) < 2:
            return 0.0
        
        # Compute Chatterjee correlation safely
        order = np.argsort(a_clean, kind='stable')
        b_s = b_clean[order]
        r = rankdata(b_s, method='ordinal')
        
        # Validate ranks
        if len(r) < 2:
            return 0.0
            
        s = np.sum(np.abs(r[1:] - r[:-1]))
        denom = n**2 - 1
        
        if denom <= 0:
            return 0.0
            
        result = 1.0 - 3.0 * s / denom
        return float(np.clip(result, 0.0, 1.0))
        
    return compute_bidirectional_lagged_correlation(x, y, max_lag, chatterjee_fn)


# ──────────────────────────────────────────────────────────────────────────────
# Correlation Matrix Building
# ──────────────────────────────────────────────────────────────────────────────

def build_correlation_matrices(X: np.ndarray, max_lag: Optional[int] = None) -> dict:
    """
    Builds correlation matrices using Pearson, Spearman, and Chatterjee metrics.
    """
    T, F = X.shape
    metric_max_lags = _get_metric_max_lags(max_lag)
    
    print(f"[Correlation] Building matrices for {F} features with lags (using {config.N_JOBS} jobs):")
    for metric, lag_val in metric_max_lags.items():
        print(f"  • {metric}: max_lag={lag_val}")
    
    feature_pairs = [(i, j) for i in range(F) for j in range(i + 1, F)]
    
    print(f"  Processing {len(feature_pairs)} feature pairs in parallel...")
    results = Parallel(n_jobs=config.N_JOBS)(
        delayed(_process_feature_pair)(i, j, X, metric_max_lags) for i, j in feature_pairs
    )
    
    matrices = {
        'pearson': np.eye(F, dtype=np.float32),
        'spearman': np.eye(F, dtype=np.float32),
        'chatterjee': np.eye(F, dtype=np.float32),
        'lag_matrix': np.zeros((F, F), dtype=np.float32),
        'causality': np.zeros((F, F), dtype=np.float32),
    }
    metric_results = {}
    
    print("  Aggregating parallel results...")
    for res in filter(None, results):
        i, j, p_res, s_res, c_res = res
        _process_correlation_results(i, j, p_res, s_res, c_res, matrices)
        metric_results[(i, j)] = {
            'pearson_results': p_res,
            'spearman_results': s_res,
            'chatterjee_results': c_res
        }
    
    print("✅ Correlation matrices built successfully")
    return {**matrices, 'metric_results': metric_results}


def _get_metric_max_lags(max_lag: Optional[int]) -> Dict[str, int]:
    """Helper to determine max lags for each metric based on config."""
    if max_lag is not None:
        return {'pearson': max_lag, 'spearman': max_lag, 'chatterjee': max_lag}
    elif config.USE_METRIC_SPECIFIC_LAGS:
        return config.METRIC_SPECIFIC_MAX_LAGS.copy()
    else:
        global_max_lag = config.MAX_LAG
        return {'pearson': global_max_lag, 'spearman': global_max_lag, 'chatterjee': global_max_lag}


def _process_feature_pair(i: int, j: int, X: np.ndarray, metric_max_lags: Dict[str, int]):
    """Process a single pair of features to compute all correlation metrics."""
    x_i, x_j = X[:, i], X[:, j]
    if len(x_i) < max(metric_max_lags.values()) + 1: return None
    
    p_results = bidirectional_lagged_pearson(x_i, x_j, metric_max_lags['pearson'])
    s_results = bidirectional_lagged_spearman(x_i, x_j, metric_max_lags['spearman'])
    c_results = bidirectional_lagged_chatterjee(x_i, x_j, metric_max_lags['chatterjee'])
    
    return i, j, p_results, s_results, c_results


def _process_correlation_results(i: int, j: int, p_res: dict, s_res: dict, c_res: dict, matrices: Dict[str, np.ndarray]):
    """Processes individual correlation results and updates matrices."""
    
    results_map = {'pearson': p_res, 'spearman': s_res, 'chatterjee': c_res}
    
    # --- Determine overall causality and lag from the three metrics ---
    causality_votes = [res['causality'] for res in [p_res, s_res, c_res]]
    strengths = [max(abs(res['x_leads_y'][1]), abs(res['y_leads_x'][1])) for res in [p_res, s_res, c_res]]
    lags = [res['x_leads_y'][0] if res['causality'] >= 0 else res['y_leads_x'][0] for res in [p_res, s_res, c_res]]
    
    # Weighted average for lag, majority vote for causality
    total_strength = sum(strengths)
    if total_strength > 1e-6:
        avg_lag = sum(lag * strength for lag, strength in zip(lags, strengths)) / total_strength
        # Weighted vote for causality
        final_causality = sum(vote * strength for vote, strength in zip(causality_votes, strengths)) / total_strength
        final_causality = 1 if final_causality > 0.33 else -1 if final_causality < -0.33 else 0
    else:
        avg_lag = 0
        final_causality = 0

    # --- Populate matrices ---
    for metric_name, res in results_map.items():
        # Choose the correlation from the direction of causality, or the stronger one if no clear causality
        if final_causality == 1: # i -> j
            corr = res['x_leads_y'][1]
        elif final_causality == -1: # j -> i
            corr = res['y_leads_x'][1]
        else: # No clear causality, take the stronger magnitude
            corr = res['x_leads_y'][1] if abs(res['x_leads_y'][1]) >= abs(res['y_leads_x'][1]) else res['y_leads_x'][1]
        matrices[metric_name][i, j] = matrices[metric_name][j, i] = corr

    matrices['lag_matrix'][i, j] = avg_lag
    matrices['lag_matrix'][j, i] = avg_lag 
    matrices['causality'][i, j] = final_causality
    matrices['causality'][j, i] = -final_causality
