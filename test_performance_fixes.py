#!/usr/bin/env python3
"""
Performance Fixes Validation Test

Test the performance improvements made to fix training bottlenecks:
- Optimized spectral clustering (no full adjacency matrix)
- Pre-created projection layers (no dynamic layer creation)
- Enhanced error handling and robustness
- Memory optimization
"""

import sys
import os
import time
import torch
import numpy as np
from torch_geometric.data import Data, Batch

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gnn_lstm import config
from gnn_lstm.models import TSAPGnnLSTM

def test_performance_improvements():
    """Test the performance improvements in TSAP model."""
    print("🚀" + "=" * 80)
    print("🔧 PERFORMANCE FIXES VALIDATION TEST")
    print("🚀" + "=" * 80)
    
    try:
        # Create test data (larger for performance testing)
        batch_size = 8
        num_nodes_per_graph = 118
        node_feature_dim = 9
        
        print(f"\n📊 Test Configuration:")
        print(f"   • Batch size: {batch_size}")
        print(f"   • Nodes per graph: {num_nodes_per_graph}")
        print(f"   • Feature dimension: {node_feature_dim}")
        print(f"   • Total nodes: {batch_size * num_nodes_per_graph}")
        
        # Create sample graphs
        graphs = []
        for _ in range(batch_size):
            x = torch.randn(num_nodes_per_graph, node_feature_dim)
            num_edges = 100  # Dense connectivity for stress testing
            edge_index = torch.randint(0, num_nodes_per_graph, (2, num_edges))
            graph = Data(x=x, edge_index=edge_index)
            graphs.append(graph)
        
        batch = Batch.from_data_list(graphs)
        
        # Initialize optimized TSAP model
        model = TSAPGnnLSTM(
            node_feature_dim=node_feature_dim,
            **config.TSAP_CONFIG
        )
        
        print(f"\n✅ Model Created Successfully")
        print(f"   • Total Parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   • Residual Pooling: {model.tsap.use_residual_pooling}")
        print(f"   • Multi-Scale Pooling: {model.tsap.use_multi_scale_pooling}")
        
        # Performance test - multiple forward passes
        model.eval()
        num_iterations = 5
        times = []
        
        print(f"\n🔧 Performance Testing ({num_iterations} iterations)...")
        
        with torch.no_grad():
            # Warmup
            _ = model(batch)
            
            # Timed iterations
            for i in range(num_iterations):
                start_time = time.time()
                output = model(batch)
                end_time = time.time()
                
                iteration_time = end_time - start_time
                times.append(iteration_time)
                
                print(f"   Iteration {i+1}: {iteration_time:.3f}s")
        
        # Performance statistics
        avg_time = np.mean(times)
        std_time = np.std(times)
        min_time = np.min(times)
        max_time = np.max(times)
        
        print(f"\n📈 Performance Results:")
        print(f"   • Average time: {avg_time:.3f}s ± {std_time:.3f}s")
        print(f"   • Min time: {min_time:.3f}s")
        print(f"   • Max time: {max_time:.3f}s")
        print(f"   • Throughput: {batch_size / avg_time:.1f} graphs/second")
        
        # Memory usage test
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated()
            
            # Forward pass
            with torch.no_grad():
                output = model(batch)
            
            peak_memory = torch.cuda.max_memory_allocated()
            memory_used = (peak_memory - initial_memory) / 1024**2  # MB
            
            print(f"\n💾 Memory Usage:")
            print(f"   • Memory used: {memory_used:.1f} MB")
            print(f"   • Peak memory: {peak_memory / 1024**2:.1f} MB")
        
        # Output validation
        print(f"\n✅ Output Validation:")
        print(f"   • Output shape: {output.shape}")
        print(f"   • Output range: [{output.min():.4f}, {output.max():.4f}]")
        print(f"   • No NaN values: {not torch.isnan(output).any()}")
        print(f"   • No Inf values: {not torch.isinf(output).any()}")
        
        # Performance assessment
        performance_rating = "EXCELLENT" if avg_time < 1.0 else "GOOD" if avg_time < 2.0 else "NEEDS_IMPROVEMENT"
        
        print(f"\n🎯 Performance Assessment: {performance_rating}")
        
        if avg_time < 1.0:
            print("✅ Performance is excellent - ready for training!")
        elif avg_time < 2.0:
            print("✅ Performance is good - suitable for training")
        else:
            print("⚠️ Performance may need further optimization")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """Test the enhanced error handling capabilities."""
    print(f"\n🛡️ ERROR HANDLING TEST")
    print("=" * 60)
    
    try:
        # Test with problematic data
        model = TSAPGnnLSTM(node_feature_dim=9, **config.TSAP_CONFIG)
        
        # Test 1: Empty batch
        try:
            empty_batch = Batch.from_data_list([])
            output = model(empty_batch)
            print("⚠️ Empty batch test: Model should handle this gracefully")
        except Exception as e:
            print(f"✅ Empty batch properly handled: {type(e).__name__}")
        
        # Test 2: Single node graph
        try:
            single_node = Data(x=torch.randn(1, 9), edge_index=torch.empty(2, 0, dtype=torch.long))
            batch = Batch.from_data_list([single_node])
            output = model(batch)
            print(f"✅ Single node graph handled: output shape {output.shape}")
        except Exception as e:
            print(f"⚠️ Single node graph issue: {e}")
        
        # Test 3: Large graph
        try:
            large_graph = Data(
                x=torch.randn(500, 9), 
                edge_index=torch.randint(0, 500, (2, 1000))
            )
            batch = Batch.from_data_list([large_graph])
            output = model(batch)
            print(f"✅ Large graph handled: output shape {output.shape}")
        except Exception as e:
            print(f"⚠️ Large graph issue: {e}")
        
        print("✅ Error handling tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def main():
    """Run all performance validation tests."""
    print("🔧" + "=" * 80)
    print("🚀 PERFORMANCE FIXES COMPREHENSIVE VALIDATION")
    print("🔧" + "=" * 80)
    
    tests = [
        ("Performance Improvements", test_performance_improvements),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*80}")
        print(f"🧪 {test_name.upper()} TEST")
        print(f"{'='*80}")
        
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print(f"\n{'='*80}")
    print(f"📋 PERFORMANCE VALIDATION SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {test_name:<30} {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"✅ All performance fixes validated! Training should be stable and fast.")
        print(f"🚀 Ready to run the full training pipeline without interruptions.")
    else:
        print(f"⚠️ Some tests failed. Please review the performance fixes.")

if __name__ == "__main__":
    main()
