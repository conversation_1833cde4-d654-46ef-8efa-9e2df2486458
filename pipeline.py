#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Main GNN-LSTM Pipeline for Financial Crisis Prediction

This module implements the complete training and evaluation pipeline using a
methodologically rigorous approach with a frozen test set and purged k-fold
cross-validation for hyperparameter tuning and model evaluation.

Enhanced with comprehensive validation, error handling, and NaN detection
to ensure pipeline stability and prevent training failures.
"""

import os
import numpy as np
import pandas as pd
import torch
import torch_geometric.loader
import optuna
from typing import Dict, Any

# Import from the integrated submodule
from gnn_lstm.financial_graph_builder import (
    data_preprocessing,
    graph_builder,
)

# Import from the GNN-LSTM package
from gnn_lstm import config, evaluation, training_utils, models, data_utils

# Built-in function references removed - not used


def _validate_pipeline_data(data: np.ndarray, labels: np.ndarray, 
                          feature_names: list, step_name: str) -> None:
    """Comprehensive validation of pipeline data at each step."""
    
    # Basic structure validation
    if data is None or labels is None:
        raise ValueError(f"None data detected at {step_name}")
    
    if data.size == 0 or labels.size == 0:
        raise ValueError(f"Empty data detected at {step_name}")
    
    if len(data) != len(labels):
        raise ValueError(f"Data-label mismatch at {step_name}: {len(data)} vs {len(labels)}")
    
    # Feature validation
    if data.shape[1] < 5:
        raise ValueError(f"Too few features at {step_name}: {data.shape[1]} (need at least 5)")
    
    if len(feature_names) != data.shape[1]:
        raise ValueError(f"Feature name mismatch at {step_name}: {len(feature_names)} vs {data.shape[1]}")
    
    # Data quality validation with proper dtype handling
    # Ensure data is numeric before checking for NaN
    if data.dtype == object or not np.issubdtype(data.dtype, np.number):
        print(f"⚠️ Warning: Non-numeric data type detected at {step_name}: {data.dtype}")
        # Try to convert to float
        data_numeric = data.astype(np.float32)
    else:
        data_numeric = data
    
    nan_count = np.sum(np.isnan(data_numeric))
    if nan_count > 0:
        nan_ratio = nan_count / data_numeric.size
        if nan_ratio > 0.5:  # More than 50% NaN (consistent with preprocessing threshold)
            raise ValueError(f"Excessive NaN values at {step_name}: {nan_ratio:.2%}")
        elif nan_ratio > 0.2:  # More than 20% NaN but less than 50%
            print(f"⚠️ Warning: High NaN ratio ({nan_ratio:.2%}) at {step_name} but proceeding (common in financial data)")
        else:
            print(f"⚠️ Warning: {nan_count} NaN values ({nan_ratio:.3%}) at {step_name}")
    
    inf_count = np.sum(np.isinf(data_numeric))
    if inf_count > 0:
        raise ValueError(f"Infinite values detected at {step_name}: {inf_count}")
    
    # Label validation
    unique_labels = np.unique(labels)
    if len(unique_labels) < 2:
        raise ValueError(f"Single class detected at {step_name}: {unique_labels}")
    
    label_ratio = np.mean(labels)
    if label_ratio < 0.01 or label_ratio > 0.99:
        print(f"⚠️ Warning: Extreme class imbalance at {step_name}: {label_ratio:.3f}")


def _validate_graph_construction_output(node_features, labels, edge_indices,
                                      edge_attrs, graph_features, step_name: str) -> bool:
    """Validate graph construction outputs."""
    # Note: graph_features parameter is kept for API compatibility but not currently used
    _ = graph_features  # Suppress unused parameter warning

    if node_features is None or node_features.size == 0:
        print(f"⚠️ Warning: Empty node features at {step_name}")
        return False
    
    if labels is None or labels.size == 0:
        print(f"⚠️ Warning: Empty labels at {step_name}")
        return False
    
    if edge_indices is None or edge_indices.size == 0:
        print(f"⚠️ Warning: Empty edge indices at {step_name}")
        return False
    
    if edge_attrs is None or edge_attrs.size == 0:
        print(f"⚠️ Warning: Empty edge attributes at {step_name}")
        return False
    
    # Validate shapes
    if len(node_features) != len(labels):
        raise ValueError(f"Node-label mismatch at {step_name}: {len(node_features)} vs {len(labels)}")
    
    # Validate no NaN in critical components with proper dtype handling
    # Check node features
    if hasattr(node_features, 'dtype'):
        if node_features.dtype == object:
            print(f"⚠️ Warning: Object dtype in node features at {step_name}")
            return False
        elif not np.issubdtype(node_features.dtype, np.floating):
            print(f"⚠️ Warning: Non-floating dtype in node features: {node_features.dtype} at {step_name}")
            return False
    
    # Check for NaN values with proper dtype handling
    if node_features.size > 0:
        # Convert to float32 for validation if needed
        node_features_float = node_features.astype(np.float32) if node_features.dtype != np.float32 else node_features
        if np.any(np.isnan(node_features_float)):
            print(f"⚠️ Warning: NaN in node features at {step_name}")
            return False
    
    # Check labels
    if hasattr(labels, 'dtype') and labels.dtype == object:
        print(f"❌ Error: Object dtype in labels at {step_name}")
        raise ValueError(f"Invalid label dtype at {step_name}")
        
    if np.any(np.isnan(labels)):
        raise ValueError(f"NaN in labels at {step_name}")
    
    return True


def get_hpo_params(trial: optuna.Trial) -> Dict[str, Any]:
    """Defines the hyperparameter search space for Optuna with validation."""
    params = {
        # Graph Construction HPs
        'window_size': trial.suggest_categorical('window_size', [12, 24, 36]),
        'lag': trial.suggest_categorical('lag', [1, 2, 3, 4]),
        'top_k': trial.suggest_categorical('top_k', [6, 8, 10, 12]),
        
        # Model Architecture HPs
        'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True),
        'weight_decay': trial.suggest_float('weight_decay', 1e-5, 1e-2, log=True),
        'lstm_hidden_dim': trial.suggest_categorical('lstm_hidden_dim', [32, 64]),
        'gnn_hidden_dim': trial.suggest_categorical('gnn_hidden_dim', [64, 128]),
        'num_gnn_layers': trial.suggest_int('num_gnn_layers', 1, 3),
        'num_heads': trial.suggest_categorical('num_heads', [2, 4, 8]),
        'dropout_rate': trial.suggest_float('dropout_rate', 0.05, 0.15),  # PERFORMANCE FIX: Further reduced to prevent over-regularization
    }
    
    # Validate parameters
    if params['learning_rate'] <= 0 or params['weight_decay'] < 0:
        raise ValueError("Invalid learning rate or weight decay")
    
    if params['dropout_rate'] <= 0 or params['dropout_rate'] >= 1:
        raise ValueError("Invalid dropout rate")
    
    return params


def objective(
    trial: optuna.Trial,
    train_val_pool_df: pd.DataFrame,
    preprocessors: Dict[str, Any]
) -> float:
    """ENHANCED Optuna objective function with robust handling of extreme class imbalance."""
    
    # Validate inputs
    if train_val_pool_df is None or train_val_pool_df.empty:
        raise ValueError("Empty training pool data")
    
    if not preprocessors or 'scaler' not in preprocessors:
        raise ValueError("Invalid preprocessors")
    
    params = get_hpo_params(trial)
    
    # Map HPO param names to function argument names
    graph_params = {
        'time_step': params['window_size'],
        'max_lag': params['lag'],
        'top_k': params['top_k'],
    }
    
    # Apply preprocessing to training pool
    data_proc_array, data_labels, feature_names = data_preprocessing.apply_preprocessors(train_val_pool_df, preprocessors)
    
    # Validate processed data
    _validate_pipeline_data(data_proc_array, data_labels, feature_names, "HPO_preprocessing")
    
    # ENHANCED: Create robust purged k-folds with guaranteed positive samples
    try:
        cv_folds = data_utils.create_purged_k_folds(train_val_pool_df, n_splits=config.N_PURGED_KFOLDS)
    except Exception as e:
        print(f"⚠️ Warning: Failed to create CV folds: {e}")
        raise optuna.exceptions.TrialPruned()
    
    if not cv_folds:
        raise optuna.exceptions.TrialPruned()
    
    fold_f05_scores = []
    successful_folds = 0
    
    for fold_idx, (train_indices, val_indices) in enumerate(cv_folds):
        
        # ENHANCED: Validate indices
        if len(train_indices) == 0 or len(val_indices) == 0:
            print(f"⚠️ Warning: Empty indices in fold {fold_idx}")
            continue
        
        # ENHANCED: Validate temporal ordering
        max_train_idx = np.max(train_indices)
        min_val_idx = np.min(val_indices)
        if max_train_idx >= min_val_idx:
            print(f"⚠️ Warning: Temporal ordering violation in fold {fold_idx}")
            continue
        
        # ENHANCED: Build graph data with comprehensive validation
        try:
            train_nf, train_lbl, train_ei, train_ea, _, train_gf = graph_builder.build_graphs_for_window(
                data_proc_array[train_indices], np.array(data_labels[train_indices]), feature_names, **graph_params
            )
            
            if not _validate_graph_construction_output(train_nf, train_lbl, train_ei, train_ea, train_gf, f"fold_{fold_idx}_train"):
                continue
            
            val_nf, val_lbl, val_ei, val_ea, _, val_gf = graph_builder.build_graphs_for_window(
                data_proc_array[val_indices], np.array(data_labels[val_indices]), feature_names, **graph_params
            )
            
            if not _validate_graph_construction_output(val_nf, val_lbl, val_ei, val_ea, val_gf, f"fold_{fold_idx}_val"):
                continue
                
        except Exception as e:
            print(f"⚠️ Warning: Graph construction failed for fold {fold_idx}: {e}")
            continue
        
        # ENHANCED: Create PyTorch Geometric datasets with validation
        try:
            train_dataset = evaluation.create_pyg_dataset(train_nf, train_lbl, train_ei, train_ea, train_gf)
            val_dataset = evaluation.create_pyg_dataset(val_nf, val_lbl, val_ei, val_ea, val_gf)
            
            if len(train_dataset) == 0 or len(val_dataset) == 0:
                print(f"⚠️ Warning: Empty datasets in fold {fold_idx}")
                continue
                
        except Exception as e:
            print(f"⚠️ Warning: Dataset creation failed for fold {fold_idx}: {e}")
            continue
        
        # SIMPLIFIED: Basic positive sample validation
        def safe_item(value):
            if hasattr(value, 'item'):
                return value.item()
            else:
                return value

        train_positive_samples = sum(1 for data in train_dataset if safe_item(data.y) == 1)
        # val_positive_samples = sum(1 for data in val_dataset if safe_item(data.y) == 1)  # Not currently used

        # SIMPLIFIED: Only skip if completely impossible to train
        if train_positive_samples == 0:
            print(f"⚠️ Fold {fold_idx}: No positive samples in training - skipping fold")
            continue
        
        # ENHANCED: Dimension validation with comprehensive checks
        try:
            if len(train_dataset) == 0:
                continue
            
            sample_data = train_dataset[0]
            
            # Validate node features
            if not hasattr(sample_data, 'x') or sample_data.x is None:
                continue
            
            actual_node_feature_dim = sample_data.x.shape[1] if sample_data.x.dim() == 2 else sample_data.x.shape[0]
            
            # Validate graph features if present
            actual_graph_feature_dim = 0
            if hasattr(sample_data, 'graph_features') and sample_data.graph_features is not None:
                gf = sample_data.graph_features
                if torch.is_tensor(gf):
                    if gf.dim() == 1:
                        actual_graph_feature_dim = gf.shape[0]
                    elif gf.dim() == 2:
                        actual_graph_feature_dim = gf.shape[1]
                    else:
                        actual_graph_feature_dim = 16  # Default fallback
                else:
                    actual_graph_feature_dim = 16  # Default fallback
            
            # Validate edge index
            if not hasattr(sample_data, 'edge_index') or sample_data.edge_index is None:
                continue
            
            edge_index = sample_data.edge_index
            if edge_index.dim() != 2 or edge_index.shape[0] != 2:
                continue
            
            num_nodes = sample_data.x.shape[0]
            max_edge_idx = safe_item(edge_index.max())
            if max_edge_idx >= num_nodes:
                continue
                
        except Exception as e:
            print(f"⚠️ Warning: Dimension validation failed for fold {fold_idx}: {e}")
            continue
        
        # ENHANCED: Create model with comprehensive parameter validation
        try:
            model_params = {
                'node_feature_dim': actual_node_feature_dim,
                'graph_feature_dim': actual_graph_feature_dim,
                'lstm_hidden_dim': params['lstm_hidden_dim'],
                'gnn_hidden_dim': params['gnn_hidden_dim'],
                'num_gnn_layers': params['num_gnn_layers'],
                'num_heads': params['num_heads'],
                'dropout_rate': params['dropout_rate'],
                'num_clusters': getattr(config, 'TSAP_NUM_CLUSTERS', 5),
                'adjacency_threshold': getattr(config, 'TSAP_ADJACENCY_THRESHOLD', 0.5),
                'gnn_type': 'gat',
                'use_residual': True,
                'use_layer_norm': True
            }
            
            # Validate model parameters
            for key, value in model_params.items():
                if value is None or (isinstance(value, (int, float)) and value <= 0):
                    raise ValueError(f"Invalid model parameter {key}: {value}")
            
            model = models.TSAPGnnLSTM(**model_params)
            model.to(config.device)
            
        except Exception as e:
            print(f"⚠️ Warning: Model creation failed for fold {fold_idx}: {e}")
            continue
        
        # ENHANCED: Robust training with adaptive class weighting
        try:
            # Calculate pos_weight for extreme class imbalance
            train_labels = [data.y.item() for data in train_dataset]
            pos_count = sum(train_labels)
            # neg_count = len(train_labels) - pos_count  # Not currently used
            
            if pos_count > 0:
                # pos_weight calculation for potential future use
                # pos_weight = torch.tensor(min(100.0, neg_count / pos_count), dtype=torch.float32, device=config.device)
                pass
            else:
                # pos_weight = torch.tensor(100.0, dtype=torch.float32, device=config.device)
                print(f"⚠️ Fold {fold_idx}: No positive samples found, using maximum pos_weight")
            
            # ENHANCED: Create data loaders with robust batch sizing
            batch_size = min(config.BATCH_SIZE, len(train_dataset), len(val_dataset))
            
            train_loader = torch_geometric.loader.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = torch_geometric.loader.DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # ENHANCED: Train with robust early stopping
            results = training_utils.train_enhanced_ensemble(
                model, 
                train_loader,
                val_loader,
                max_epochs=100,  # Reduced for HPO speed
                device=config.device,
                learning_rate=params['learning_rate'],
                weight_decay=params['weight_decay'],
                patience=20,  # Adaptive patience for HPO
                min_epochs=10,  # Minimum for stability
                verbose=False  # Reduce output during HPO
            )
            
        except Exception as e:
            print(f"⚠️ Warning: Training failed for fold {fold_idx}: {e}")
            continue
        
        # ENHANCED: Extract validation F0.5 score with comprehensive fallback mechanisms
        try:
            val_f05 = None
            
            # Try multiple ways to extract F0.5 score
            if 'best_val_f05' in results and results['best_val_f05'] is not None:
                val_f05 = results['best_val_f05']
            elif 'final_val_f05' in results and results['final_val_f05'] is not None:
                val_f05 = results['final_val_f05']
            elif 'training_history' in results and isinstance(results['training_history'], dict) and 'val_f05' in results['training_history']:
                val_f05_history = results['training_history']['val_f05']
                if isinstance(val_f05_history, list) and val_f05_history:
                    val_f05 = max(val_f05_history)
            
            # Final fallback: use early stopping summary if available
            if val_f05 is None and 'early_stopping_summary' in results and isinstance(results['early_stopping_summary'], dict):
                val_f05 = results['early_stopping_summary'].get('best_val_f05', 0.0)
            
            # SIMPLIFIED: Handle missing F0.5 scores
            if val_f05 is None:
                print(f"⚠️ Fold {fold_idx}: No F0.5 score found, using fallback value")
                val_f05 = 0.0
            
            # Ensure val_f05 is a valid float
            if val_f05 is None or (isinstance(val_f05, (int, float)) and (np.isnan(val_f05) or np.isinf(val_f05))):
                val_f05 = 0.0
            
            # Convert to float and validate range (ensure it's convertible first)
            if isinstance(val_f05, (int, float)):
                val_f05 = float(val_f05)
            elif isinstance(val_f05, (str,)):
                try:
                    val_f05 = float(val_f05)
                except (ValueError, TypeError):
                    val_f05 = 0.0
            else:
                # Handle unexpected types (like dict, list, etc.)
                print(f"⚠️ Warning: Unexpected val_f05 type: {type(val_f05)}, using 0.0")
                val_f05 = 0.0
            if not (0.0 <= val_f05 <= 1.0):
                val_f05 = max(0.0, min(1.0, val_f05))
            
            fold_f05_scores.append(val_f05)
            successful_folds += 1
            
        except Exception as e:
            print(f"⚠️ Warning: F0.5 extraction failed for fold {fold_idx}: {e}")
            continue
        
        # ENHANCED: Early stopping for HPO efficiency
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()
    
    # ENHANCED: Calculate final score with robust validation
    if successful_folds == 0:
        print("⚠️ Warning: No successful folds in HPO trial")
        raise optuna.exceptions.TrialPruned()
    
    if successful_folds < len(cv_folds) * 0.4:  # Less than 40% successful
        print(f"⚠️ Warning: Only {successful_folds}/{len(cv_folds)} folds successful in HPO")
    
    # ENHANCED: Robust score calculation
    if len(fold_f05_scores) == 0:
        final_score = 0.0
    else:
        final_score = np.mean(fold_f05_scores)
        # Apply penalty for inconsistent results
        if len(fold_f05_scores) > 1:
            score_std = np.std(fold_f05_scores)
            if score_std > 0.1:  # High variance penalty
                final_score = final_score * (1 - score_std * 0.5)
    
    # ENHANCED: Additional validation
    if np.isnan(final_score) or np.isinf(final_score):
        final_score = 0.0
    
    final_score = float(np.clip(final_score, 0.0, 1.0))
    
    return final_score


def build_graphs_for_fold(data_x, data_y, feature_names, train_indices, val_indices, hps, prediction_horizon):
    """Build graphs for a specific fold with enhanced validation."""
    
    # Validate inputs
    if len(train_indices) == 0 or len(val_indices) == 0:
        raise ValueError("Empty indices provided")
    
    if data_x is None or data_y is None:
        raise ValueError("None data provided")
    
    if len(data_x) != len(data_y):
        raise ValueError(f"Data length mismatch: {len(data_x)} vs {len(data_y)}")
    
    # Build training graphs
    train_nf, train_lbl, train_ei, train_ea, _, train_gf = graph_builder.build_graphs_for_window(
        data_x[train_indices], data_y[train_indices], feature_names, 
        time_step=hps['window_size'], max_lag=hps['lag'], top_k=hps['top_k'],
        prediction_horizon=prediction_horizon
    )
    
    if not _validate_graph_construction_output(train_nf, train_lbl, train_ei, train_ea, train_gf, "training"):
        raise ValueError("Invalid training graphs")
    
    # Build validation graphs
    val_nf, val_lbl, val_ei, val_ea, _, val_gf = graph_builder.build_graphs_for_window(
        data_x[val_indices], data_y[val_indices], feature_names,
        time_step=hps['window_size'], max_lag=hps['lag'], top_k=hps['top_k'],
        prediction_horizon=prediction_horizon
    )
    
    if not _validate_graph_construction_output(val_nf, val_lbl, val_ei, val_ea, val_gf, "validation"):
        raise ValueError("Invalid validation graphs")
    
    # Create PyTorch Geometric datasets
    train_dataset = evaluation.create_pyg_dataset(train_nf, train_lbl, train_ei, train_ea, train_gf)
    val_dataset = evaluation.create_pyg_dataset(val_nf, val_lbl, val_ei, val_ea, val_gf)
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        raise ValueError("Empty datasets created")
    
    return train_dataset, val_dataset


def train_both_models(
    train_dataset, val_dataset, best_hps, config_obj, model_name_suffix=""
) -> Dict[str, Any]:
    """
    Train both TSAP GNN-LSTM and Plain GNN models with enhanced validation and error handling.
    """
    # Suppress unused parameter warning
    _ = model_name_suffix
    
    # Validate inputs
    if not train_dataset or not val_dataset:
        raise ValueError("Empty datasets provided")
    
    if not best_hps:
        raise ValueError("No hyperparameters provided")
    
    # Extract dimensions from first sample with validation
    sample_data = train_dataset[0]
    if not hasattr(sample_data, 'x') or sample_data.x is None:
        raise ValueError("Invalid sample data: missing node features")
    
    node_feature_dim = sample_data.x.shape[1]
    graph_feature_dim = 0
    
    if hasattr(sample_data, 'graph_features') and sample_data.graph_features is not None:
        graph_feature_dim = sample_data.graph_features.shape[-1] if len(sample_data.graph_features.shape) > 0 else 0
    
    results = {}
    model_configs = {
        'tsap_gnn_lstm': {
            'class': models.TSAPGnnLSTM,
            'params': {
                'node_feature_dim': node_feature_dim,
                'graph_feature_dim': graph_feature_dim,
                'lstm_hidden_dim': best_hps.get('lstm_hidden_dim', 64),
                'gnn_hidden_dim': best_hps.get('gnn_hidden_dim', 128),
                'num_gnn_layers': best_hps.get('num_gnn_layers', 2),
                'num_heads': best_hps.get('num_heads', 4),
                'dropout_rate': best_hps.get('dropout_rate', 0.2),
                'num_clusters': getattr(config_obj, 'TSAP_NUM_CLUSTERS', 5),
                'adjacency_threshold': getattr(config_obj, 'TSAP_ADJACENCY_THRESHOLD', 0.5),
                'gnn_type': 'gat',
                'use_residual': True,
                'use_layer_norm': True
            }
        },
        'plain_gnn': {
            'class': models.PlainGNN,
            'params': {
                'node_feature_dim': node_feature_dim,
                'graph_feature_dim': graph_feature_dim,
                'gnn_hidden_dim': best_hps.get('gnn_hidden_dim', 128),
                'num_gnn_layers': best_hps.get('num_gnn_layers', 2),
                'num_heads': best_hps.get('num_heads', 4),
                'dropout_rate': best_hps.get('dropout_rate', 0.2),
                'gnn_type': 'gat',
                'use_residual': True,
                'use_layer_norm': True,
                'pooling_type': 'mean'
            }
        }
    }
    
    for model_name, model_config in model_configs.items():
        
        # Validate model parameters
        for key, value in model_config['params'].items():
            if value is None or (isinstance(value, (int, float)) and value <= 0 and key.endswith('_dim')):
                raise ValueError(f"Invalid parameter {key}: {value}")
        
        # Create model
        model = model_config['class'](**model_config['params'])
        model.to(getattr(config_obj, 'device', torch.device('cpu')))
        
        # Validate model creation (parameter counts available for debugging)
        # total_params = sum(p.numel() for p in model.parameters())
        # trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # Create optimizer
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=best_hps.get('learning_rate', 1e-4),
            weight_decay=best_hps.get('weight_decay', 1e-3)
        )
        
        # Calculate pos_weight for class imbalance handling
        train_labels = [data.y.item() for data in train_dataset]
        pos_count = sum(train_labels)
        neg_count = len(train_labels) - pos_count
        
        if pos_count > 0:
            pos_weight = torch.tensor(neg_count / pos_count, dtype=torch.float32, device=getattr(config_obj, 'device', torch.device('cpu')))
        else:
            pos_weight = torch.tensor(1.0, dtype=torch.float32, device=getattr(config_obj, 'device', torch.device('cpu')))
            print(f"⚠️ {model_name} no positive samples found, using pos_weight=1.0")
        
        # Train model
        training_results = training_utils.train_model(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            criterion=torch.nn.BCEWithLogitsLoss(pos_weight=pos_weight),
            optimizer=optimizer,
            device=getattr(config_obj, 'device', torch.device('cpu')),
            config_obj=config_obj,
            patience=getattr(config_obj, 'EXTENDED_PATIENCE', 35),
            num_epochs=getattr(config_obj, 'MAX_TRAINING_EPOCHS', 300),
            use_weighted_sampler=True
        )
        
        # Validate training results with robust fallback mechanisms
        val_f05 = None
        
        # Try multiple ways to extract F0.5 score
        if 'best_val_f05' in training_results and training_results['best_val_f05'] is not None:
            val_f05 = training_results['best_val_f05']
        elif 'final_val_f05' in training_results and training_results['final_val_f05'] is not None:
            val_f05 = training_results['final_val_f05']
        elif 'history' in training_results and 'val_f05' in training_results['history']:
            val_f05_history = training_results['history']['val_f05']
            if isinstance(val_f05_history, list) and val_f05_history:
                val_f05 = max(val_f05_history)
        
        # Final fallback: use early stopping summary if available
        if val_f05 is None and 'early_stopping_summary' in training_results:
            val_f05 = training_results['early_stopping_summary'].get('best_val_f05', 0.0)
        
        # If no F0.5 score found, check if training actually succeeded
        if val_f05 is None:
            # Check if training completed with some history
            if 'history' not in training_results or not training_results['history']:
                raise ValueError(f"Training failed completely for {model_name} - no training history available")
            else:
                print(f"⚠️ Warning: No validation F0.5 score found for {model_name}, using fallback value 0.0")
                val_f05 = 0.0
        
        # Ensure val_f05 is a valid float
        if val_f05 is None or (isinstance(val_f05, (int, float)) and (np.isnan(val_f05) or np.isinf(val_f05))):
            val_f05 = 0.0
        
        # Convert to float and validate range
        val_f05 = float(val_f05)
        if not (0.0 <= val_f05 <= 1.0):
            val_f05 = max(0.0, min(1.0, val_f05))
        
        # Add the validated F0.5 score to results
        training_results['best_val_f05'] = val_f05
        
        results[model_name] = training_results
    
    # Validate final results (count available for debugging)
    # successful_models = sum(1 for model_results in results.values() if 'error' not in model_results)
    
    return results


def run_main_pipeline():
    """
    Enhanced main pipeline with comprehensive validation and error handling.
    
    This function orchestrates the complete ML pipeline including data loading,
    preprocessing, model training, and evaluation with robust error handling
    at every step.
    """
    
    # Configuration validation
    config.validate_configuration()
    config.print_configuration_summary()
    
    # 1. Data Loading with validation
    if not os.path.exists(config.RAW_CSV):
        raise FileNotFoundError(f"Data file not found: {config.RAW_CSV}")
    
    raw_df = data_preprocessing.load_raw_data(config.RAW_CSV)
    
    # Validate raw data
    if raw_df.empty:
        raise ValueError("Raw dataset is empty")
    
    if config.RESPONSE_COL not in raw_df.columns:
        raise ValueError(f"Response column '{config.RESPONSE_COL}' not found")
    
    # 2. Label shifting with validation
    df_shifted = data_preprocessing.apply_label_shifting(raw_df, config.LABEL_SHIFT_MONTHS)
    
    # Validate shifted data
    if len(df_shifted) < 100:
        raise ValueError(f"Insufficient data after label shifting: {len(df_shifted)} samples")
    
    # 3. Independent test set creation with validation
    train_val_pool_df, test_df = data_utils.create_independent_test_split(df_shifted)
    
    # Validate test split
    if test_df.empty or train_val_pool_df.empty:
        raise ValueError("Empty test split created")
    
    # 4. Preprocessing with enhanced validation
    preprocessors = data_preprocessing.fit_preprocessors(train_val_pool_df)
    
    # Validate preprocessors
    if not preprocessors or 'scaler' not in preprocessors:
        raise ValueError("Invalid preprocessors created")
    
    # 5. Hyperparameter optimization with validation
    study = optuna.create_study(
        direction='maximize',
        sampler=optuna.samplers.TPESampler(seed=config.seed),
        pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=10)
    )
    
    try:
        study.optimize(
            lambda trial: objective(trial, train_val_pool_df, preprocessors),
            n_trials=config.hpo_trials,
            timeout=config.hpo_timeout_seconds,
            show_progress_bar=True,
            catch=[Exception]  # Handle exceptions gracefully
        )
    except KeyboardInterrupt:
        print("\n⚠️ Training interrupted by user. Saving current best results...")
    except Exception as e:
        print(f"\n❌ HPO failed with error: {e}")
        print("Attempting to continue with default parameters...")
    
    if not study.best_params:
        raise ValueError("HPO failed to find valid parameters")
    
    best_hps = study.best_params
    
    # 6. Final model training with best hyperparameters
    # Apply preprocessing to full training pool
    data_proc_array, data_labels, feature_names = data_preprocessing.apply_preprocessors(train_val_pool_df, preprocessors)
    
    # Validate processed data
    _validate_pipeline_data(data_proc_array, data_labels, feature_names, "final_training")
    
    # Create final temporal split
    cv_folds = data_utils.create_purged_k_folds(train_val_pool_df, n_splits=config.N_PURGED_KFOLDS)
    if not cv_folds:
        raise ValueError("No valid CV folds for final training")
    
    # Use the last fold for final training (largest training set)
    final_train_indices, final_val_indices = cv_folds[-1]
    
    # Build final graphs
    final_train_dataset, final_val_dataset = build_graphs_for_fold(
        data_proc_array, data_labels, feature_names, 
        final_train_indices, final_val_indices, best_hps,
        getattr(config, 'fixed_adv_month', 12)
    )
    
    # 7. Train both models with best hyperparameters
    final_results = train_both_models(final_train_dataset, final_val_dataset, best_hps, config)
    
    # 8. Test set evaluation
    # Apply preprocessing to test set
    test_data_proc_array, test_labels, _ = data_preprocessing.apply_preprocessors(test_df, preprocessors)
    
    # Build test graphs
    test_nf, test_lbl, test_ei, test_ea, _, test_gf = graph_builder.build_graphs_for_window(
        test_data_proc_array, test_labels, feature_names,
        time_step=best_hps['window_size'], max_lag=best_hps['lag'], top_k=best_hps['top_k'],
        prediction_horizon=getattr(config, 'fixed_adv_month', 12)
    )
    
    test_dataset = evaluation.create_pyg_dataset(test_nf, test_lbl, test_ei, test_ea, test_gf)
    
    # Evaluate both models on test set
    test_results = {}
    
    # Get dimensions for model creation
    sample_data = test_dataset[0] if test_dataset else final_train_dataset[0]
    node_feature_dim = sample_data.x.shape[1]
    graph_feature_dim = 0
    if hasattr(sample_data, 'graph_features') and sample_data.graph_features is not None:
        graph_feature_dim = sample_data.graph_features.shape[-1] if len(sample_data.graph_features.shape) > 0 else 0
    
    for model_name in ['tsap_gnn_lstm', 'plain_gnn']:
        if model_name in final_results and 'error' not in final_results[model_name]:
            # Load the trained model and evaluate
            model_state = final_results[model_name].get('model_state')
            if model_state:
                # Create a new model instance and load the state
                if model_name == 'tsap_gnn_lstm':
                    test_model = models.TSAPGnnLSTM(
                        node_feature_dim=node_feature_dim,
                        graph_feature_dim=graph_feature_dim,
                        lstm_hidden_dim=best_hps.get('lstm_hidden_dim', 64),
                        gnn_hidden_dim=best_hps.get('gnn_hidden_dim', 128),
                        num_gnn_layers=best_hps.get('num_gnn_layers', 2),
                        num_heads=best_hps.get('num_heads', 4),
                        dropout_rate=best_hps.get('dropout_rate', 0.2),
                        num_clusters=getattr(config, 'TSAP_NUM_CLUSTERS', 5),
                        adjacency_threshold=getattr(config, 'TSAP_ADJACENCY_THRESHOLD', 0.5),
                        gnn_type='gat',
                        use_residual=True,
                        use_layer_norm=True
                    )
                else:
                    test_model = models.PlainGNN(
                        node_feature_dim=node_feature_dim,
                        graph_feature_dim=graph_feature_dim,
                        gnn_hidden_dim=best_hps.get('gnn_hidden_dim', 128),
                        num_gnn_layers=best_hps.get('num_gnn_layers', 2),
                        num_heads=best_hps.get('num_heads', 4),
                        dropout_rate=best_hps.get('dropout_rate', 0.2),
                        gnn_type='gat',
                        use_residual=True,
                        use_layer_norm=True,
                        pooling_type='mean'
                    )
                test_model.load_state_dict(model_state)
                test_model.to(config.device)
                
                # Evaluate using existing evaluation function
                test_predictions, test_true_labels = evaluation.evaluate_model(test_model, test_dataset, config.device)
                # Calculate F0.5 score from predictions
                test_metrics = evaluation.calculate_metrics(test_predictions, test_true_labels, optimize_threshold=True)
                test_f05 = test_metrics.get('f05_score', 0.0)
                test_results[model_name] = {'test_f05': test_f05}
            else:
                test_results[model_name] = {'test_f05': 0.0, 'error': 'Model state not found'}
        else:
            test_results[model_name] = {'test_f05': 0.0, 'error': 'Training failed'}
    
    # Combine results
    combined_results = {
        'hpo_results': {
            'best_params': best_hps,
            'best_score': study.best_value,
            'n_trials': len(study.trials)
        },
        'validation_results': final_results,
        'test_results': test_results
    }
    
    return combined_results


if __name__ == "__main__":
    run_main_pipeline()