#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Entry Point for GNN-LSTM Financial Crisis Prediction System

This module serves as the enhanced main entry point for the GNN-LSTM financial crisis 
prediction system, providing comprehensive validation, error handling, and 
system diagnostics to ensure robust execution.

Features:
- System environment validation
- Dependency checking
- Configuration validation
- Enhanced error handling and logging
- Performance monitoring

IMPORTANT:
- All try/except/finally blocks have been removed per user policy.
- All errors and exceptions will surface naturally and halt execution.
- No error suppression, warning filtering, or silent handling is present.
- This ensures full transparency and immediate visibility of all issues.
"""

import sys
import os
import time
import traceback
from typing import Dict, Any, List, TypedDict

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Define type structures for validation results
class ValidationSection(TypedDict):
    status: str
    details: Dict[str, Any]
    issues: List[str]

class ValidationResults(TypedDict):
    python_version: ValidationSection
    dependencies: ValidationSection
    hardware: ValidationSection
    file_system: ValidationSection

# Import all dependencies unconditionally at module level
import torch
TORCH_AVAILABLE = True
import numpy as np
NUMPY_AVAILABLE = True
import pandas as pd
PANDAS_AVAILABLE = True
from pipeline import run_main_pipeline
PIPELINE_AVAILABLE = True
from gnn_lstm import config
CONFIG_AVAILABLE = True

# Overall dependency status
DEPENDENCIES_AVAILABLE = all([TORCH_AVAILABLE, NUMPY_AVAILABLE, PANDAS_AVAILABLE, PIPELINE_AVAILABLE, CONFIG_AVAILABLE])

# Comprehensive results import
from comprehensive_results_report import print_comprehensive_results


def validate_system_requirements() -> Dict[str, Any]:
    """Comprehensive system validation with detailed reporting."""
    validation_results = {
        'python_version': {
            'status': 'unknown',
            'details': {},
            'issues': []
        },
        'dependencies': {
            'status': 'unknown',
            'details': {},
            'issues': []
        },
        'hardware': {
            'status': 'unknown',
            'details': {},
            'issues': []
        },
        'file_system': {
            'status': 'unknown',
            'details': {},
            'issues': []
        }
    }
    
    # 1. Python version validation
    python_version = sys.version_info
    validation_results['python_version']['details'] = {
        'version': f"{python_version.major}.{python_version.minor}.{python_version.micro}",
        'implementation': sys.implementation.name
    }
    
    if python_version.major != 3:
        validation_results['python_version']['issues'].append("Python 3.x required")
        validation_results['python_version']['status'] = 'error'
    elif python_version.minor < 8:
        validation_results['python_version']['issues'].append("Python 3.8+ recommended")
        validation_results['python_version']['status'] = 'warning'
    else:
        validation_results['python_version']['status'] = 'ok'
    
    # 2. Dependencies validation
    if not DEPENDENCIES_AVAILABLE:
        validation_results['dependencies']['status'] = 'error'
        validation_results['dependencies']['issues'].append("Core dependencies missing")
    else:
        dependency_info = {}
        dependency_info['torch'] = torch.__version__
        if torch.cuda.is_available():
            dependency_info['cuda_available'] = True
            dependency_info['cuda_devices'] = torch.cuda.device_count()
        else:
            dependency_info['cuda_available'] = False
        dependency_info['pandas'] = pd.__version__
        dependency_info['numpy'] = np.__version__
        import sklearn
        dependency_info['sklearn'] = sklearn.__version__
        validation_results['dependencies']['details'] = dependency_info
        critical_deps = ['torch', 'pandas', 'numpy', 'sklearn']
        missing_deps = [dep for dep in critical_deps if dependency_info.get(dep) == "Not available"]
        if missing_deps:
            validation_results['dependencies']['status'] = 'error'
            validation_results['dependencies']['issues'].extend([f"Missing {dep}" for dep in missing_deps])
        else:
            validation_results['dependencies']['status'] = 'ok'
    
    # 3. Hardware validation
    import psutil
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    cpu_count = psutil.cpu_count()
    hardware_info = {
        'memory_total_gb': round(memory_gb, 2),
        'memory_available_gb': round(memory.available / (1024**3), 2),
        'cpu_count': cpu_count,
        'cpu_usage_percent': psutil.cpu_percent(interval=1)
    }
    validation_results['hardware']['details'] = hardware_info
    if memory_gb < 4:
        validation_results['hardware']['issues'].append("Low memory (< 4GB)")
        validation_results['hardware']['status'] = 'warning'
    elif memory_gb < 8:
        validation_results['hardware']['issues'].append("Recommended: 8GB+ RAM")
        validation_results['hardware']['status'] = 'warning'
    else:
        validation_results['hardware']['status'] = 'ok'
    
    # 4. File system validation
    current_dir = os.getcwd()
    test_file = os.path.join(current_dir, '.test_write_permissions')
    with open(test_file, 'w') as f:
        f.write('test')
    os.remove(test_file)
    write_permissions = True
    file_system_info = {
        'current_directory': current_dir,
        'write_permissions': write_permissions,
        'data_directory_exists': os.path.exists('data') if CONFIG_AVAILABLE and config is not None and hasattr(config, 'RAW_CSV') else False,
        'results_directory_exists': os.path.exists('results') if CONFIG_AVAILABLE and config is not None and hasattr(config, 'RESULTS_DIR') else False
    }
    validation_results['file_system']['details'] = file_system_info
    if not write_permissions:
        validation_results['file_system']['status'] = 'error'
        validation_results['file_system']['issues'].append("No write permissions in current directory")
    else:
        validation_results['file_system']['status'] = 'ok'
    
    # Overall status
    statuses = [section['status'] for section in validation_results.values() if isinstance(section, dict) and 'status' in section]
    if 'error' in statuses:
        overall_status = 'error'
    elif 'warning' in statuses:
        overall_status = 'warning'
    else:
        overall_status = 'ok'
        
    # Create a properly typed result with overall_status as a string
    final_results: Dict[str, Any] = dict(validation_results)
    final_results['overall_status'] = overall_status
    
    return final_results


def print_system_info():
    """Print detailed system information for diagnostics."""
    print("\n" + "="*60)
    print("🖥️ SYSTEM INFORMATION")
    print("="*60)
    
    print(f"Platform: {sys.platform}")
    print(f"Python executable: {sys.executable}")
    print(f"Working directory: {os.getcwd()}")
    
    if DEPENDENCIES_AVAILABLE and TORCH_AVAILABLE and torch is not None:
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA devices: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  Device {i}: {torch.cuda.get_device_name(i)}")
    
    print("="*60)


def handle_pipeline_error(error: Exception, validation_results: Dict[str, Any]) -> None:
    """Enhanced error handling with system diagnostics."""
    print(f"\n❌ PIPELINE ERROR: {type(error).__name__}")
    print(f"Message: {str(error)}")
    
    # Print system validation issues if any
    all_issues = []
    for section_name, section_data in validation_results.items():
        if section_name != 'overall_status' and isinstance(section_data, dict) and section_data.get('issues'):
            all_issues.extend([f"{section_name}: {issue}" for issue in section_data['issues']])
    
    if all_issues:
        print(f"\n⚠️ System issues that may be related:")
        for issue in all_issues:
            print(f"   • {issue}")
    
    print(f"\n🔍 Full traceback:")
    traceback.print_exc()
    
    # Suggest common solutions
    print(f"\n💡 Common solutions:")
    print(f"   • Check data file exists and is readable")
    print(f"   • Ensure sufficient memory is available")
    print(f"   • Verify all dependencies are properly installed")
    print(f"   • Check write permissions in current directory")
    print(f"   • Try reducing batch size or model complexity")


def main():
    """Enhanced main function with comprehensive validation and error handling."""
    start_time = time.time()
    
    # 1. System validation
    validation_results = validate_system_requirements()
    
    if validation_results['overall_status'] == 'error':
        print(f"\n❌ Critical system issues detected. Cannot proceed.")
        return 1
    elif validation_results['overall_status'] == 'warning':
        print(f"\n⚠️ System warnings detected. Proceeding with caution...")
    else:
        print(f"\n✅ System validation passed. Ready to proceed.")
    
    # 2. Print system info for diagnostics
    if os.getenv('VERBOSE', 'false').lower() == 'true':
        print_system_info()
    
    # 3. Configuration validation
    if not DEPENDENCIES_AVAILABLE:
        raise ImportError("Core dependencies not available")
    
    if not CONFIG_AVAILABLE:
        raise ImportError("Configuration not available")
    
    config.validate_configuration()
    
    # 4. Execute main pipeline
    pipeline_start = time.time()
    
    results = None
    # Run the pipeline (already imported at top)
    results = run_main_pipeline()
    
    pipeline_duration = time.time() - pipeline_start
    
    # 5. Results summary - Use comprehensive format
    if results and isinstance(results, dict):
        print(f"\n🎉 PIPELINE COMPLETED SUCCESSFULLY!")
        print(f"   ⏱️ Total execution time: {pipeline_duration:.1f} seconds ({pipeline_duration/60:.1f} minutes)")
        
        # Use comprehensive results reporting instead of simple summary
        print(f"\n{'='*80}")
        print_comprehensive_results()
        print(f"{'='*80}")
        
        # Validation status
        validation_status = "PASSED" if results.get('performance_validation', {}).get('overall_status') == 'pass' else "NEEDS_REVIEW"
        print(f"\n📋 Overall Performance Validation: {validation_status}")
    elif results:
        print(f"\n⚠️ Pipeline returned non-dict results: {type(results)}")
    else:
        print(f"\n⚠️ Pipeline returned None results")
    
    total_duration = time.time() - start_time
    print(f"\n⏱️ Total program execution time: {total_duration:.1f} seconds")
    return 0


if __name__ == "__main__":
    """Entry point with proper error handling."""
    exit_code = main()
    sys.exit(exit_code)