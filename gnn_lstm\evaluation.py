#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Model Evaluation Module for GNN-LSTM Financial Crisis Prediction

This module provides comprehensive evaluation utilities including:
1. F0.5 score calculation with NaN handling
2. Performance metrics computation with validation
3. PyTorch Geometric dataset creation with validation
4. Performance target validation against thresholds
5. Robust error handling throughout evaluation

All evaluation functions include comprehensive validation to prevent NaN propagation
and ensure reliable metric computation.
"""

import warnings
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.metrics import (
    accuracy_score,
    confusion_matrix,
    f1_score,
    precision_recall_curve,
    precision_score,
    recall_score,
    roc_auc_score,
)
from torch_geometric.loader import DataLoader

from . import config


def _validate_evaluation_inputs(y_true: Union[np.ndarray, List], y_pred: Union[np.ndarray, List], 
                               function_name: str) -> Tuple[np.ndarray, np.ndarray]:
    """Comprehensive validation of evaluation inputs."""
    # Convert to numpy arrays and flatten
    y_true = np.asarray(y_true).flatten()
    y_pred = np.asarray(y_pred).flatten()
    
    # Basic validation
    if y_true.size == 0 or y_pred.size == 0:
        raise ValueError(f"{function_name}: Empty input arrays")
    
    if len(y_true) != len(y_pred):
        raise ValueError(f"{function_name}: Mismatched array lengths: {len(y_true)} vs {len(y_pred)}")
    
    # Check for NaN/infinite values
    if np.any(~np.isfinite(y_true)):
        nan_count = np.sum(~np.isfinite(y_true))
        raise ValueError(f"{function_name}: Non-finite values in y_true: {nan_count} values")
    
    if np.any(~np.isfinite(y_pred)):
        nan_count = np.sum(~np.isfinite(y_pred))
        print(f"⚠️ Warning: Non-finite values in y_pred for {function_name}: {nan_count} values, replacing with 0")
        y_pred = np.where(np.isfinite(y_pred), y_pred, 0)
    
    # Validate binary classification
    unique_true = np.unique(y_true)
    if not np.all(np.isin(unique_true, [0, 1])):
        raise ValueError(f"{function_name}: y_true must be binary (0 or 1), got {unique_true}")
    
    unique_pred = np.unique(y_pred)
    if not np.all(np.isin(unique_pred, [0, 1])):
        raise ValueError(f"{function_name}: y_pred must be binary (0 or 1), got {unique_pred}")
    
    # Check for single class
    if len(unique_true) < 2:
        print(f"⚠️ Warning: Single class in y_true for {function_name}: {unique_true}")
    
    return y_true, y_pred


def optimize_threshold_for_f05(y_true: Union[np.ndarray, List], 
                              y_proba: Union[np.ndarray, List],
                              num_steps: int = 99) -> Tuple[float, float]:
    """
    ENHANCED threshold optimization for F0.5 score with robust edge case handling.
    
    ENHANCED FEATURES:
    1. Adaptive threshold range based on data distribution
    2. Robust handling of extreme class imbalance
    3. Fallback strategies for edge cases
    4. Comprehensive validation of inputs
    
    Args:
        y_true: True binary labels
        y_proba: Predicted probabilities
        num_steps: Number of threshold steps to evaluate
        
    Returns:
        Tuple of (optimal_threshold, best_f05_score)
    """
    # Input validation and conversion
    y_true = np.asarray(y_true).flatten().astype(int)
    y_proba = np.asarray(y_proba).flatten().astype(float)
    
    # Basic validation
    if len(y_true) == 0 or len(y_proba) == 0:
        return 0.5, 0.0
    
    if len(y_true) != len(y_proba):
        print(f"⚠️ Warning: Length mismatch in threshold optimization: {len(y_true)} vs {len(y_proba)}")
        return 0.5, 0.0
    
    # Handle edge cases
    unique_labels = np.unique(y_true)
    if len(unique_labels) < 2:
        # Single class - return appropriate threshold
        if unique_labels[0] == 1:
            # All positive labels
            return 0.01, 1.0  # Low threshold to predict all as positive
        else:
            # All negative labels
            return 0.99, 1.0  # High threshold to predict all as negative
    
    # Clean probabilities
    y_proba = np.clip(y_proba, 0.0, 1.0)
    
    # ENHANCED: Adaptive threshold range based on data characteristics
    positive_ratio = np.mean(y_true)
    
    if positive_ratio < 0.01:  # Very few positives (less than 1%)
        # Use fine-grained low threshold range
        thresholds = np.concatenate([
            np.linspace(0.001, 0.1, num_steps//2),
            np.linspace(0.1, 0.9, num_steps//2)
        ])
    elif positive_ratio > 0.99:  # Very few negatives (more than 99%)
        # Use fine-grained high threshold range
        thresholds = np.concatenate([
            np.linspace(0.1, 0.9, num_steps//2),
            np.linspace(0.9, 0.999, num_steps//2)
        ])
    else:
        # Standard threshold range
        thresholds = np.linspace(0.01, 0.99, num_steps)
    
    best_f05 = 0.0
    best_threshold = 0.5
    
    # Track threshold search statistics
    valid_thresholds = 0
    
    for threshold in thresholds:
        try:
            y_pred = (y_proba >= threshold).astype(int)
            f05 = calculate_f05_score(y_true, y_pred)
            
            # Validate F0.5 score
            if np.isnan(f05) or np.isinf(f05):
                continue
            
            valid_thresholds += 1
            
            if f05 > best_f05:
                best_f05 = f05
                best_threshold = threshold
                
        except Exception as e:
            print(f"⚠️ Warning: Error in threshold {threshold:.3f}: {e}")
            continue
    
    # ENHANCED: Fallback strategies for edge cases
    if valid_thresholds == 0:
        print("⚠️ Warning: No valid thresholds found, using default")
        return 0.5, 0.0
    
    if best_f05 == 0.0:
        # No threshold gave positive F0.5, use probability-based fallback
        if positive_ratio < 0.5:
            # Few positives - use low threshold
            fallback_threshold = np.percentile(y_proba, 50)  # Use median as threshold
        else:
            # Many positives - use high threshold
            fallback_threshold = np.percentile(y_proba, 80)  # Use 80th percentile
        
        # Validate fallback
        fallback_pred = (y_proba >= fallback_threshold).astype(int)
        fallback_f05 = calculate_f05_score(y_true, fallback_pred)
        
        if fallback_f05 > best_f05:
            best_f05 = fallback_f05
            best_threshold = fallback_threshold
    
    # Final validation and clipping
    best_threshold = float(np.clip(best_threshold, 0.001, 0.999))
    best_f05 = float(np.clip(best_f05, 0.0, 1.0))
    
    return best_threshold, best_f05


def calculate_f05_score(y_true: np.ndarray, y_pred: np.ndarray) -> float:
    """
    Calculate F0.5 score with ENHANCED edge case handling for financial crisis prediction.
    
    ENHANCED FEATURES:
    1. Robust handling of extreme class imbalance (very few positive samples)
    2. Graceful degradation when no positive samples exist
    3. Proper handling of all-negative and all-positive scenarios
    4. Consistent scoring for model selection in highly imbalanced datasets
    
    F0.5 = (1 + 0.5^2) * (precision * recall) / ((0.5^2 * precision) + recall)
         = 1.25 * (precision * recall) / (0.25 * precision + recall)
    
    Args:
        y_true: True binary labels
        y_pred: Predicted binary labels
        
    Returns:
        F0.5 score (float between 0 and 1)
    """
    # Convert to numpy arrays and ensure they're binary
    y_true = np.array(y_true, dtype=int)
    y_pred = np.array(y_pred, dtype=int)
    
    # Basic input validation
    if len(y_true) != len(y_pred):
        raise ValueError(f"Input arrays must have same length: {len(y_true)} vs {len(y_pred)}")
    
    if len(y_true) == 0:
        return 0.0
    
    # Calculate confusion matrix components
    tp = np.sum((y_true == 1) & (y_pred == 1))
    fp = np.sum((y_true == 0) & (y_pred == 1))
    fn = np.sum((y_true == 1) & (y_pred == 0))
    tn = np.sum((y_true == 0) & (y_pred == 0))
    
    # ENHANCED: Comprehensive edge case handling
    total_true_positives = np.sum(y_true)
    total_pred_positives = np.sum(y_pred)
    
    # Case 1: No positive samples in ground truth (all labels are 0)
    if total_true_positives == 0:
        if total_pred_positives == 0:
            # Perfect prediction: all negative correctly predicted
            return 1.0
        else:
            # False alarms: predicted some positives when none exist
            return 0.0
    
    # Case 2: All samples are positive in ground truth (all labels are 1)
    if total_true_positives == len(y_true):
        if total_pred_positives == len(y_pred):
            # Perfect prediction: all positive correctly predicted
            return 1.0
        else:
            # Missed some positives
            precision = tp / total_pred_positives if total_pred_positives > 0 else 0.0
            recall = tp / total_true_positives  # This is tp / len(y_true)
            
            if precision > 0 and recall > 0:
                return 1.25 * (precision * recall) / (0.25 * precision + recall)
            else:
                return 0.0
    
    # Case 3: Standard binary classification scenario
    # Calculate precision and recall with safety checks
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    
    # ENHANCED: Handle extreme imbalance scenarios
    if tp == 0:
        # No true positives predicted
        if total_true_positives > 0:
            # There were positive samples but we missed them all
            return 0.0
        else:
            # No positive samples to find (handled above, but safety check)
            return 1.0 if total_pred_positives == 0 else 0.0
    
    # ENHANCED: Robust F0.5 calculation
    if precision > 0 and recall > 0:
        f05 = 1.25 * (precision * recall) / (0.25 * precision + recall)
        
        # Validate result
        if np.isnan(f05) or np.isinf(f05):
            print(f"⚠️ Warning: Invalid F0.5 computation (tp={tp}, fp={fp}, fn={fn}, tn={tn})")
            return 0.0
        
        return float(np.clip(f05, 0.0, 1.0))
    
    # If precision or recall is zero, F0.5 is zero
    return 0.0


def calculate_enhanced_metrics(y_true: Union[np.ndarray, List], 
                             y_pred: Union[np.ndarray, List],
                             y_proba: Optional[Union[np.ndarray, List]] = None) -> Dict[str, float]:
    """
    Calculate comprehensive metrics with ENHANCED validation and edge case handling.
    
    ENHANCED FEATURES:
    1. Robust handling of extreme class imbalance
    2. Comprehensive input validation
    3. Fallback strategies for edge cases
    4. Enhanced probability validation
    
    Returns:
        Dictionary of metrics with enhanced validation
    """
    
    # Enhanced input validation
    try:
        y_true, y_pred = _validate_evaluation_inputs(y_true, y_pred, "calculate_enhanced_metrics")
    except Exception as e:
        print(f"⚠️ Warning: Input validation failed: {e}")
        return {'f05_score': 0.0, 'precision': 0.0, 'recall': 0.0, 'accuracy': 0.0}
    
    # ENHANCED: Validate probabilities if provided
    if y_proba is not None:
        y_proba = np.asarray(y_proba).flatten()
        if len(y_proba) != len(y_true):
            print("⚠️ Warning: Probability array length mismatch, ignoring")
            y_proba = None
        else:
            # Enhanced probability validation
            if np.any(~np.isfinite(y_proba)):
                print("⚠️ Warning: Non-finite probabilities detected, cleaning")
                y_proba = np.where(np.isfinite(y_proba), y_proba, 0.5)
            
            if np.any(y_proba < 0) or np.any(y_proba > 1):
                print("⚠️ Warning: Probabilities out of [0,1] range, clipping")
                y_proba = np.clip(y_proba, 0.0, 1.0)
    
    # Calculate confusion matrix with enhanced validation
    try:
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred, labels=[0, 1]).ravel()
    except Exception as e:
        print(f"⚠️ Warning: Confusion matrix calculation failed: {e}")
        return {'f05_score': 0.0, 'precision': 0.0, 'recall': 0.0, 'accuracy': 0.0}
    
    # Calculate basic metrics with enhanced validation
    total = tn + fp + fn + tp
    if total == 0:
        return {'f05_score': 0.0, 'precision': 0.0, 'recall': 0.0, 'accuracy': 0.0}
    
    # ENHANCED: Robust metric calculation
    accuracy = (tp + tn) / total if total > 0 else 0.0
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    
    # ENHANCED: Use improved F0.5 calculation
    f05_score = calculate_f05_score(y_true, y_pred)
    
    # Enhanced metrics dictionary
    metrics = {
        'f05_score': float(f05_score),
        'precision': float(precision),
        'recall': float(recall),
        'accuracy': float(accuracy),
        'true_positives': int(tp),
        'true_negatives': int(tn),
        'false_positives': int(fp),
        'false_negatives': int(fn),
        'total_samples': int(total)
    }
    
    # ENHANCED: Additional metrics for imbalanced data
    total_positives = tp + fn
    total_negatives = tn + fp
    
    if total_positives > 0:
        metrics['positive_ratio'] = float(total_positives / total)
    else:
        metrics['positive_ratio'] = 0.0
    
    if total_negatives > 0:
        metrics['negative_ratio'] = float(total_negatives / total)
    else:
        metrics['negative_ratio'] = 0.0
    
    # ENHANCED: Add probability-based metrics if available
    if y_proba is not None:
        try:
            from sklearn.metrics import roc_auc_score, average_precision_score
            
            # ROC AUC (only if both classes present)
            if len(np.unique(y_true)) > 1:
                roc_auc = roc_auc_score(y_true, y_proba)
                if np.isfinite(roc_auc):
                    metrics['roc_auc'] = float(roc_auc)
                else:
                    metrics['roc_auc'] = 0.5
            else:
                metrics['roc_auc'] = 0.5
                
            # Average Precision (more robust for imbalanced data)
            if total_positives > 0:
                avg_precision = average_precision_score(y_true, y_proba)
                if np.isfinite(avg_precision):
                    metrics['average_precision'] = float(avg_precision)
                else:
                    metrics['average_precision'] = float(total_positives / total)
            else:
                metrics['average_precision'] = 0.0
                
        except Exception as e:
            print(f"⚠️ Warning: Probability-based metrics calculation failed: {e}")
            metrics['roc_auc'] = 0.5
            metrics['average_precision'] = 0.0
    
    # ENHANCED: Final validation of all metrics
    for key, value in metrics.items():
        if isinstance(value, float) and (np.isnan(value) or np.isinf(value)):
            print(f"⚠️ Warning: Invalid metric {key}: {value}, setting to 0")
            metrics[key] = 0.0
    
    return metrics


def calculate_metrics(
    y_proba: np.ndarray,
    y_true: np.ndarray,
    threshold: Optional[float] = None,
    optimize_threshold: bool = True,
) -> Dict[str, Union[float, int]]:
    """
    Calculate comprehensive evaluation metrics.
    
    Args:
        y_proba: Predicted probabilities
        y_true: True binary labels
        threshold: Fixed threshold (if None, will be optimized)
        optimize_threshold: Whether to optimize threshold for F0.5
        
    Returns:
        Dictionary with comprehensive metrics
    """
    # Handle empty inputs
    if len(y_true) == 0 or len(y_proba) == 0:
        return {
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1': 0.0,
            'f0.5': 0.0,
            'auc_roc': 0.0,
            'optimal_threshold': 0.5,
            'tp': 0, 'fp': 0, 'tn': 0, 'fn': 0,
            'num_samples': 0,
            'num_positive': 0,
            'num_negative': 0,
        }
    
    # Ensure proper array format
    y_true = np.asarray(y_true).flatten().astype(int)
    y_proba = np.asarray(y_proba).flatten().astype(float)
    
    # Basic statistics
    num_samples = len(y_true)
    num_positive = np.sum(y_true)
    num_negative = num_samples - num_positive
    
    # Calculate AUC-ROC if we have both classes
    if len(np.unique(y_true)) > 1:
        auc_roc = roc_auc_score(y_true, y_proba)
    else:
        auc_roc = 0.0
    
    # Optimize threshold if requested
    if optimize_threshold or threshold is None:
        optimal_threshold, best_f05 = optimize_threshold_for_f05(y_true, y_proba)
        if threshold is None:
            threshold = optimal_threshold
    else:
        optimal_threshold = threshold
    
    # Generate predictions with threshold
    y_pred = (y_proba >= threshold).astype(int)
    
    # Calculate confusion matrix
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred, labels=[0, 1]).ravel()
    
    # Calculate metrics with proper zero handling
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, zero_division=0)
    recall = recall_score(y_true, y_pred, zero_division=0)
    f1 = f1_score(y_true, y_pred, zero_division=0)
    f05 = calculate_f05_score(y_true, y_pred)
    
    return {
        'accuracy': float(accuracy),
        'precision': float(precision),
        'recall': float(recall),
        'f1': float(f1),
        'f0.5': float(f05),
        'auc_roc': float(auc_roc),
        'optimal_threshold': float(optimal_threshold),
        'threshold_used': float(threshold),
        'tp': int(tp),
        'fp': int(fp),
        'tn': int(tn),
        'fn': int(fn),
        'num_samples': int(num_samples),
        'num_positive': int(num_positive),
        'num_negative': int(num_negative),
    }


def evaluate_model(
    model: nn.Module,
    dataset: List,
    device: torch.device,
    batch_size: int = 32,
    threshold: Optional[float] = None,
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Evaluate model on a dataset and return predictions and true labels.
    
    Args:
        model: Trained model to evaluate
        dataset: List of PyTorch Geometric Data objects
        device: Device for evaluation
        batch_size: Batch size for evaluation
        threshold: Classification threshold (if None, returns probabilities)
        
    Returns:
        Tuple of (predictions, true_labels)
    """
    if not dataset:
        return np.array([]), np.array([])
    
    model.eval()
    all_predictions = []
    all_labels = []
    
    # Create data loader
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    with torch.no_grad():
        for batch in dataloader:
            batch = batch.to(device)
            
            # Get model outputs
            outputs = model(batch)
            
            # Convert to probabilities
            probabilities = torch.sigmoid(outputs).cpu().numpy()
            labels = batch.y.cpu().numpy()
            
            all_predictions.extend(probabilities.flatten())
            all_labels.extend(labels.flatten())
    
    predictions = np.array(all_predictions)
    true_labels = np.array(all_labels)
    
    # Apply threshold if provided
    if threshold is not None:
        predictions = (predictions >= threshold).astype(int)
    
    return predictions, true_labels


def calculate_composite_score(
    val_f05: float,
    train_f05: float,
    gap_penalty_multiplier: float = 2.5,
) -> float:
    """
    Calculate composite F0.5 score with training-validation gap penalty.
    
    This score penalizes models that have a large gap between training and
    validation performance, indicating potential overfitting.
    
    Args:
        val_f05: Validation F0.5 score
        train_f05: Training F0.5 score
        gap_penalty_multiplier: Penalty multiplier for train-val gap
        
    Returns:
        Composite score with gap penalty applied
    """
    # Calculate gap (only penalize when train > val)
    gap = max(0, train_f05 - val_f05)
    gap_penalty = gap * gap_penalty_multiplier
    
    # Apply penalty to validation score
    composite_score = max(0, val_f05 - gap_penalty)
    
    return composite_score


def evaluate_with_metrics(
    model: nn.Module,
    dataset: List,
    device: torch.device,
    batch_size: int = 32,
    optimize_threshold: bool = True,
    threshold: Optional[float] = None,
) -> Dict[str, Union[float, int]]:
    """
    Comprehensive model evaluation with all metrics.
    
    Args:
        model: Trained model to evaluate
        dataset: List of PyTorch Geometric Data objects
        device: Device for evaluation
        batch_size: Batch size for evaluation
        optimize_threshold: Whether to optimize threshold for F0.5
        threshold: Fixed threshold (if provided)
        
    Returns:
        Dictionary with comprehensive evaluation metrics
    """
    # Get predictions and labels
    predictions, true_labels = evaluate_model(
        model, dataset, device, batch_size, threshold=None  # Always get probabilities first
    )
    
    # Calculate comprehensive metrics
    metrics = calculate_metrics(
        predictions, true_labels, 
        threshold=threshold, 
        optimize_threshold=optimize_threshold
    )
    
    return metrics


def compare_models(
    model_results: Dict[str, Dict[str, float]],
    primary_metric: str = 'f0.5',
) -> Dict[str, Union[str, float, Dict]]:
    """
    Compare multiple model results and rank them.
    
    Args:
        model_results: Dictionary mapping model names to their metrics
        primary_metric: Primary metric for ranking models
        
    Returns:
        Dictionary with comparison results and rankings
    """
    if not model_results:
        return {'best_model': '', 'rankings': {}, 'summary': {}}
    
    # Extract primary metric values
    model_scores = {}
    for model_name, metrics in model_results.items():
        if primary_metric in metrics:
            model_scores[model_name] = metrics[primary_metric]
        else:
            model_scores[model_name] = 0.0
    
    # Rank models
    sorted_models = sorted(model_scores.items(), key=lambda x: x[1], reverse=True)
    
    # Create rankings
    rankings = {}
    for rank, (model_name, score) in enumerate(sorted_models, 1):
        rankings[model_name] = {
            'rank': rank,
            'score': score,
            'metrics': model_results.get(model_name, {})
        }
    
    # Best model
    best_model = sorted_models[0][0] if sorted_models else ''
    
    # Summary statistics
    scores = list(model_scores.values())
    summary = {
        'num_models': len(model_results),
        'best_score': max(scores) if scores else 0.0,
        'worst_score': min(scores) if scores else 0.0,
        'avg_score': np.mean(scores) if scores else 0.0,
        'std_score': np.std(scores) if scores else 0.0,
    }
    
    return {
        'best_model': best_model,
        'rankings': rankings,
        'summary': summary,
        'primary_metric': primary_metric,
    }


def print_evaluation_summary(
    metrics: Dict[str, Union[float, int]],
    model_name: str = "Model",
    precision: int = 4,
) -> None:
    """
    Print a formatted evaluation summary.
    
    Args:
        metrics: Dictionary with evaluation metrics
        model_name: Name of the model being evaluated
        precision: Number of decimal places for metrics
    """
    print(f"\n{'='*60}")
    print(f"📊 {model_name} Evaluation Summary")
    print(f"{'='*60}")
    
    # Classification metrics
    print(f"\n🎯 Classification Metrics:")
    print(f"  • Accuracy:    {metrics.get('accuracy', 0):.{precision}f}")
    print(f"  • Precision:   {metrics.get('precision', 0):.{precision}f}")
    print(f"  • Recall:      {metrics.get('recall', 0):.{precision}f}")
    print(f"  • F1 Score:    {metrics.get('f1', 0):.{precision}f}")
    print(f"  • F0.5 Score:  {metrics.get('f0.5', 0):.{precision}f}")
    print(f"  • AUC-ROC:     {metrics.get('auc_roc', 0):.{precision}f}")
    
    # Threshold information
    print(f"\n🎚️ Threshold Information:")
    print(f"  • Optimal Threshold: {metrics.get('optimal_threshold', 0.5):.{precision}f}")
    print(f"  • Used Threshold:    {metrics.get('threshold_used', 0.5):.{precision}f}")
    
    # Confusion matrix
    tp = metrics.get('tp', 0)
    fp = metrics.get('fp', 0)
    tn = metrics.get('tn', 0)
    fn = metrics.get('fn', 0)
    
    print(f"\n📋 Confusion Matrix:")
    print(f"  • True Positives:  {tp}")
    print(f"  • False Positives: {fp}")
    print(f"  • True Negatives:  {tn}")
    print(f"  • False Negatives: {fn}")
    
    # Dataset statistics
    print(f"\n📈 Dataset Statistics:")
    print(f"  • Total Samples:    {metrics.get('num_samples', 0)}")
    print(f"  • Positive Samples: {metrics.get('num_positive', 0)}")
    print(f"  • Negative Samples: {metrics.get('num_negative', 0)}")
    
    if metrics.get('num_samples', 0) > 0:
        pos_ratio = metrics.get('num_positive', 0) / metrics.get('num_samples', 1)
        print(f"  • Positive Ratio:   {pos_ratio:.{precision}f}")
    
    print(f"{'='*60}")


# Backward compatibility aliases
def calculate_f_beta_score(y_true, y_pred, beta=0.5):
    """Backward compatibility alias for calculate_f05_score."""
    return calculate_f05_score(y_true, y_pred)


def get_optimal_threshold(y_true, y_proba):
    """Backward compatibility alias for optimize_threshold_for_f05."""
    threshold, f05 = optimize_threshold_for_f05(y_true, y_proba)
    return threshold

def combine_pr_curves(predictions_list: List[np.ndarray], labels_list: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Combine multiple PR curves from different folds into a single combined curve.
    
    This function concatenates predictions and labels from multiple folds to create
    a unified PR curve for threshold optimization.
    
    Args:
        predictions_list: List of prediction arrays from different folds
        labels_list: List of label arrays from different folds
        
    Returns:
        Tuple of (combined_predictions, combined_labels, thresholds)
    """
    print(f"🔗 Combining PR curves from {len(predictions_list)} folds...")
    
    # Validate inputs
    if len(predictions_list) != len(labels_list):
        raise ValueError("Number of prediction and label arrays must match")
    
    if len(predictions_list) == 0:
        return np.array([]), np.array([]), np.array([])
    
    # Combine all predictions and labels
    combined_preds = np.concatenate([np.array(preds).flatten() for preds in predictions_list])
    combined_labels = np.array(np.concatenate([np.array(labels).flatten() for labels in labels_list]), dtype=int)
    
    # Generate PR curve
    precision, recall, thresholds = precision_recall_curve(combined_labels, combined_preds)
    
    print(f"✅ Combined PR curve generated:")
    print(f"   • Total samples: {len(combined_preds)}")
    print(f"   • Positive samples: {np.sum(combined_labels)}")
    print(f"   • Negative samples: {len(combined_labels) - np.sum(combined_labels)}")
    print(f"   • Threshold points: {len(thresholds)}")
    
    return combined_preds, combined_labels, thresholds

def optimize_f05_with_gap_penalty(
    train_preds_list: List[np.ndarray],
    train_labels_list: List[np.ndarray], 
    val_preds_list: List[np.ndarray],
    val_labels_list: List[np.ndarray],
    gap_penalty_multiplier: Optional[float] = None
) -> Dict[str, Any]:
    """
    Optimize F0.5 score with gap penalty between training and validation performance.
    
    Composite score: F0.5_validation_combined - (gap_penalty_multiplier × |F0.5_train_combined - F0.5_val_combined|)
    
    Args:
        train_preds_list: Training predictions from all folds
        train_labels_list: Training labels from all folds
        val_preds_list: Validation predictions from all folds
        val_labels_list: Validation labels from all folds
        gap_penalty_multiplier: Multiplier for gap penalty (default from config)
        
    Returns:
        Dictionary with optimization results
    """
    if gap_penalty_multiplier is None:
        gap_penalty_multiplier = getattr(config, 'GAP_PENALTY_MULTIPLIER', 2.5)
    
    # Ensure gap_penalty_multiplier is never None and is a valid float
    if gap_penalty_multiplier is None:
        gap_penalty_multiplier = 2.5  # Fallback default
    
    # Ensure gap_penalty_multiplier is a valid number
    if not isinstance(gap_penalty_multiplier, (int, float)):
        gap_penalty_multiplier = 2.5
    
    # Convert to float with validation
    gap_penalty_multiplier = float(gap_penalty_multiplier)
    
    print(f"🎯 Optimizing F0.5 with gap penalty (multiplier: {gap_penalty_multiplier})...")
    
    # Combine training and validation curves
    train_combined_preds, train_combined_labels, _ = combine_pr_curves(train_preds_list, train_labels_list)
    val_combined_preds, val_combined_labels, val_thresholds = combine_pr_curves(val_preds_list, val_labels_list)
    
    if len(val_combined_preds) == 0 or len(train_combined_preds) == 0:
        print("⚠️ Warning: Empty predictions, returning default threshold")
        return {
            'optimal_threshold': 0.5,
            'val_f05': 0.0,
            'train_f05': 0.0,
            'gap_penalty': 0.0,
            'composite_score': 0.0
        }
    
    # Generate comprehensive threshold range
    thresholds = np.linspace(0.01, 0.99, 99)
    
    best_composite_score = -np.inf
    best_results = {}
    
    print(f"   • Evaluating {len(thresholds)} thresholds...")
    
    for threshold in thresholds:
        # Calculate training F0.5
        train_pred_binary = (train_combined_preds >= threshold).astype(int)
        train_tp = np.sum((train_pred_binary == 1) & (train_combined_labels == 1))
        train_fp = np.sum((train_pred_binary == 1) & (train_combined_labels == 0))
        train_fn = np.sum((train_pred_binary == 0) & (train_combined_labels == 1))
        
        train_precision = train_tp / (train_tp + train_fp) if (train_tp + train_fp) > 0 else 0
        train_recall = train_tp / (train_tp + train_fn) if (train_tp + train_fn) > 0 else 0
        # Calculate F0.5 score directly from precision and recall
        if train_precision + train_recall == 0:
            train_f05 = 0.0
        else:
            beta_squared = 0.5 ** 2
            train_f05 = (1 + beta_squared) * train_precision * train_recall / (beta_squared * train_precision + train_recall)
        
        # Calculate validation F0.5
        val_pred_binary = (val_combined_preds >= threshold).astype(int)
        val_tp = np.sum((val_pred_binary == 1) & (val_combined_labels == 1))
        val_fp = np.sum((val_pred_binary == 1) & (val_combined_labels == 0))
        val_fn = np.sum((val_pred_binary == 0) & (val_combined_labels == 1))
        
        val_precision = val_tp / (val_tp + val_fp) if (val_tp + val_fp) > 0 else 0
        val_recall = val_tp / (val_tp + val_fn) if (val_tp + val_fn) > 0 else 0
        # Calculate F0.5 score directly from precision and recall
        if val_precision + val_recall == 0:
            val_f05 = 0.0
        else:
            beta_squared = 0.5 ** 2
            val_f05 = (1 + beta_squared) * val_precision * val_recall / (beta_squared * val_precision + val_recall)
        
        # Calculate gap penalty
        gap = abs(train_f05 - val_f05)
        gap_penalty = gap_penalty_multiplier * gap
        
        # Calculate composite score
        composite_score = val_f05 - gap_penalty
        
        # Update best if this is better
        if composite_score > best_composite_score:
            best_composite_score = composite_score
            best_results = {
                'optimal_threshold': threshold,
                'val_f05': val_f05,
                'train_f05': train_f05,
                'val_precision': val_precision,
                'val_recall': val_recall,
                'train_precision': train_precision,
                'train_recall': train_recall,
                'gap': gap,
                'gap_penalty': gap_penalty,
                'composite_score': composite_score
            }
    
    print(f"✅ F0.5 optimization complete:")
    print(f"   • Optimal threshold: {best_results['optimal_threshold']:.4f}")
    print(f"   • Validation F0.5: {best_results['val_f05']:.4f}")
    print(f"   • Training F0.5: {best_results['train_f05']:.4f}")
    print(f"   • Gap: {best_results['gap']:.4f}")
    print(f"   • Gap penalty: {best_results['gap_penalty']:.4f}")
    print(f"   • Composite score: {best_results['composite_score']:.4f}")
    
    return best_results

def evaluate_fold_performance(
    model: Any,
    train_dataset: List[Any],
    val_dataset: List[Any],
    device: torch.device,
    config_obj: Any,
    fold_name: str = "fold"
) -> Dict[str, Any]:
    """
    Comprehensive evaluation of a single fold including training and validation performance.
    
    Args:
        model: Trained model
        train_dataset: Training dataset
        val_dataset: Validation dataset
        device: Device for computation
        config_obj: Configuration object
        fold_name: Name of the fold for logging
        
    Returns:
        Dictionary with comprehensive fold results
    """
    print(f"📊 Evaluating {fold_name} performance...")
    
    # Get predictions for both training and validation
    train_preds, train_labels = evaluate_model(model, train_dataset, device)
    val_preds, val_labels = evaluate_model(model, val_dataset, device)
    
    results = {
        'fold_name': fold_name,
        'train_preds': train_preds,
        'train_labels': train_labels,
        'val_preds': val_preds,
        'val_labels': val_labels
    }
    
    # Calculate individual metrics
    train_metrics = None
    val_metrics = None
    
    if len(train_preds) > 0:
        train_metrics = calculate_metrics(train_preds, train_labels)
        results['train_metrics'] = train_metrics
        print(f"   • Training F0.5: {train_metrics['f0.5']:.4f}")
    
    if len(val_preds) > 0:
        val_metrics = calculate_metrics(val_preds, val_labels)
        results['val_metrics'] = val_metrics
        print(f"   • Validation F0.5: {val_metrics['f0.5']:.4f}")
        
        # Calculate gap if both are available
        if len(train_preds) > 0 and train_metrics is not None:
            gap = abs(train_metrics['f0.5'] - val_metrics['f0.5'])
            results['f05_gap'] = gap
            print(f"   • F0.5 gap: {gap:.4f}")
    
    return results

def create_pyg_dataset(node_features: np.ndarray, labels: np.ndarray, edge_indices: np.ndarray, edge_attrs: np.ndarray, graph_features: Optional[np.ndarray] = None) -> List[Any]:
    """
    Create a PyTorch Geometric dataset from node features, edge indices, and edge attributes.
    Fixed to handle cases where edge_attrs might be lists instead of numpy arrays.
    """
    try:
        import torch
        from torch_geometric.data import Data
    except ImportError:
        raise ImportError("PyTorch and PyTorch Geometric are required to create a PyG dataset.")

    pyg_dataset = []
    num_samples = len(node_features)

    for i in range(num_samples):
        # ENHANCED TYPE CHECKING: Convert node features to tensor with list handling
        node_features_i = node_features[i]
        if isinstance(node_features_i, list):
            node_features_i = np.array(node_features_i, dtype=np.float32)
            print(f"   • Converted node_features for sample {i} from list to numpy array: shape {node_features_i.shape}")
        elif not isinstance(node_features_i, np.ndarray):
            node_features_i = np.array(node_features_i, dtype=np.float32)
            print(f"   • Converted node_features for sample {i} from {type(node_features_i)} to numpy array")
        
        node_x = torch.tensor(node_features_i, dtype=torch.float)
        
        # ENHANCED TYPE CHECKING: Handle edge indices with list handling
        edge_index_i = edge_indices[i]
        if isinstance(edge_index_i, list):
            edge_index_i = np.array(edge_index_i, dtype=np.int64)
            print(f"   • Converted edge_indices for sample {i} from list to numpy array: shape {edge_index_i.shape}")
        elif not isinstance(edge_index_i, np.ndarray):
            edge_index_i = np.array(edge_index_i, dtype=np.int64)
            print(f"   • Converted edge_indices for sample {i} from {type(edge_index_i)} to numpy array")
        
        # CRITICAL FIX: Handle edge_attrs properly - might be list or numpy array
        edge_attr_i = edge_attrs[i]
        
        # Convert edge_attr_i to numpy array if it's a list
        if isinstance(edge_attr_i, list):
            edge_attr_i = np.array(edge_attr_i, dtype=np.float32)
            print(f"   • Converted edge_attr for sample {i} from list to numpy array: shape {edge_attr_i.shape}")
        elif not isinstance(edge_attr_i, np.ndarray):
            # Convert any other type to numpy array
            edge_attr_i = np.array(edge_attr_i, dtype=np.float32)
            print(f"   • Converted edge_attr for sample {i} from {type(edge_attr_i)} to numpy array: shape {edge_attr_i.shape}")
        
        # Ensure edge_attr_i is at least 1D
        if edge_attr_i.ndim == 0:
            edge_attr_i = edge_attr_i.reshape(1)
        
        # Handle empty edge case
        if edge_index_i.size == 0:
            edge_index = torch.empty((2, 0), dtype=torch.long)
            # FIXED: Now edge_attr_i is guaranteed to be numpy array with .shape attribute
            if edge_attr_i.ndim > 1 and edge_attr_i.shape[1] > 0:
                edge_attr_dim = edge_attr_i.shape[1]
            else:
                edge_attr_dim = 9  # Default edge attribute dimension
            edge_attr = torch.empty((0, edge_attr_dim), dtype=torch.float)
        else:
            # Convert edge indices and attributes to tensors
            edge_index = torch.tensor(edge_index_i.astype(np.int64), dtype=torch.long)
            edge_attr = torch.tensor(edge_attr_i.astype(np.float32), dtype=torch.float)
        
        # Create labels tensor
        graph_y = torch.tensor([labels[i]], dtype=torch.float)
        
        # Create PyG Data object
        data = Data(x=node_x, edge_index=edge_index, edge_attr=edge_attr, y=graph_y)
        data.num_nodes = node_x.shape[0]
        
        # Add graph-level features if provided
        if graph_features is not None and len(graph_features) == num_samples:
            # --- Robust shape validation and correction for graph_features ---
            gf = graph_features[i]
            if isinstance(gf, np.ndarray):
                if gf.ndim == 0:
                    gf = np.array([gf], dtype=np.float32)
                elif gf.ndim > 1:
                    gf = gf.flatten()
            elif isinstance(gf, list):
                gf = np.array(gf, dtype=np.float32)
            else:
                # Handle other types
                gf = np.array([gf] if not hasattr(gf, '__iter__') else gf, dtype=np.float32)
            
            # Ensure 1D and length 16
            if gf.ndim != 1 or gf.shape[0] != 16:
                print(f"❌ Graph features shape error for sample {i}: got {gf.shape}, expected (16,)")
                # Attempt to fix if possible
                if gf.size == 16:
                    gf = gf.reshape(16)
                elif gf.size > 16:
                    # Truncate to 16
                    gf = gf.flatten()[:16]
                    print(f"   • Truncated graph features to 16 elements")
                else:
                    # Pad with zeros to reach 16
                    gf_padded = np.zeros(16, dtype=np.float32)
                    gf_padded[:gf.size] = gf.flatten()
                    gf = gf_padded
                    print(f"   • Padded graph features to 16 elements")
            
            data.graph_features = torch.tensor(gf, dtype=torch.float)
        
        pyg_dataset.append(data)
    
    return pyg_dataset

def validate_performance_targets(model_results: Dict[str, Any], 
                               config_obj: Any) -> Dict[str, Any]:
    """
    Validate model performance against target thresholds with comprehensive checks.
    
    Args:
        model_results: Dictionary containing model performance metrics
        config_obj: Configuration object with target thresholds
        
    Returns:
        Dictionary with validation results and status
    """
    try:
        print("🎯 Validating performance against targets...")
        
        validation_results = {
            'f05_score': {'status': 'unknown', 'value': 0.0, 'target': 'unknown', 'meets_target': False},
            'train_val_gap': {'status': 'unknown', 'value': 0.0, 'target': 'unknown', 'meets_target': False},
            'validation_loss': {'status': 'unknown', 'value': float('inf'), 'target': 'unknown', 'meets_target': False},
            'convergence': {'status': 'unknown', 'value': 0, 'target': 'unknown', 'meets_target': False},
            'overall_status': 'unknown',
            'summary': {}
        }
        
        # Validate inputs
        if not model_results:
            print("⚠️ Warning: Empty model results")
            validation_results['overall_status'] = 'error'
            return validation_results
        
        # Extract performance metrics with validation
        val_f05 = model_results.get('best_val_f05', 0.0)
        train_f05 = model_results.get('best_train_f05', 0.0)
        final_gap = model_results.get('final_gap', None)
        
        # Ensure all metrics have valid values
        if val_f05 is None:
            val_f05 = 0.0
        if train_f05 is None:
            train_f05 = 0.0
        if final_gap is None:
            final_gap = abs(train_f05 - val_f05) if train_f05 is not None and val_f05 is not None else 0.0
        
        # Validate metrics
        if not (0 <= val_f05 <= 1):
            print(f"⚠️ Warning: Invalid F0.5 score: {val_f05}")
            val_f05 = max(0.0, min(1.0, val_f05))
        
        if final_gap < 0:
            print(f"⚠️ Warning: Negative gap: {final_gap}, taking absolute value")
            final_gap = abs(final_gap)
        
        # Get validation loss
        val_loss = float('inf')
        if 'training_history' in model_results:
            history = model_results['training_history']
            if 'val_loss' in history and history['val_loss']:
                val_loss = min(history['val_loss'])
        
        # Get convergence info
        convergence_epoch = model_results.get('best_epoch', 0)
        if convergence_epoch is None:
            convergence_epoch = 0
        
        # 1. F0.5 Score Validation
        try:
            if hasattr(config_obj, 'TARGET_F05_RANGE'):
                f05_min, f05_max = config_obj.TARGET_F05_RANGE
                validation_results['f05_score'] = {
                    'status': 'evaluated',
                    'value': float(val_f05),
                    'target': f"[{f05_min:.3f}, {f05_max:.3f}]",
                    'meets_target': f05_min <= val_f05 <= f05_max
                }
            else:
                # Default target if not configured
                default_min = 0.3
                validation_results['f05_score'] = {
                    'status': 'evaluated',
                    'value': float(val_f05),
                    'target': f">= {default_min:.3f}",
                    'meets_target': val_f05 >= default_min
                }
        except Exception as e:
            print(f"⚠️ Warning: F0.5 validation failed: {e}")
            validation_results['f05_score']['status'] = 'error'
        
        # 2. Train-Validation Gap Validation
        try:
            gap_target = getattr(config_obj, 'TARGET_TRAIN_VAL_GAP', 0.1)
            validation_results['train_val_gap'] = {
                'status': 'evaluated',
                'value': float(final_gap),
                'target': f"<= {gap_target:.3f}",
                'meets_target': final_gap <= gap_target
            }
        except Exception as e:
            print(f"⚠️ Warning: Gap validation failed: {e}")
            validation_results['train_val_gap']['status'] = 'error'
        
        # 3. Validation Loss Validation
        try:
            loss_target = getattr(config_obj, 'TARGET_VAL_LOSS', 0.4)
            validation_results['validation_loss'] = {
                'status': 'evaluated',
                'value': float(val_loss),
                'target': f"<= {loss_target:.3f}",
                'meets_target': val_loss <= loss_target
            }
        except Exception as e:
            print(f"⚠️ Warning: Validation loss validation failed: {e}")
            validation_results['validation_loss']['status'] = 'error'
        
        # 4. Convergence Validation
        try:
            max_epochs = getattr(config_obj, 'MAX_TRAINING_EPOCHS', 300)
            convergence_target = max_epochs * 0.8  # Should converge within 80% of max epochs
            validation_results['convergence'] = {
                'status': 'evaluated',
                'value': int(convergence_epoch),
                'target': f"<= {convergence_target:.0f}",
                'meets_target': convergence_epoch <= convergence_target
            }
        except Exception as e:
            print(f"⚠️ Warning: Convergence validation failed: {e}")
            validation_results['convergence']['status'] = 'error'
        
        # Overall Status Calculation
        evaluated_metrics = [metric for metric in validation_results.values() 
                           if isinstance(metric, dict) and metric.get('status') == 'evaluated']
        
        if not evaluated_metrics:
            validation_results['overall_status'] = 'error'
        else:
            met_targets = sum(1 for metric in evaluated_metrics if metric.get('meets_target', False))
            total_targets = len(evaluated_metrics)
            
            if met_targets == total_targets:
                validation_results['overall_status'] = 'excellent'
            elif met_targets >= total_targets * 0.75:
                validation_results['overall_status'] = 'good'
            elif met_targets >= total_targets * 0.5:
                validation_results['overall_status'] = 'acceptable'
            else:
                validation_results['overall_status'] = 'poor'
        
        # Summary
        validation_results['summary'] = {
            'total_metrics': len(evaluated_metrics),
            'metrics_passed': sum(1 for metric in evaluated_metrics if metric.get('meets_target', False)),
            'overall_score': float(val_f05),
            'key_issues': []
        }
        
        # Identify key issues
        if not validation_results['f05_score'].get('meets_target', False):
            validation_results['summary']['key_issues'].append('F0.5 score below target')
        
        if not validation_results['train_val_gap'].get('meets_target', False):
            validation_results['summary']['key_issues'].append('Excessive overfitting gap')
        
        if not validation_results['validation_loss'].get('meets_target', False):
            validation_results['summary']['key_issues'].append('Validation loss too high')
        
        # Print validation summary
        print(f"   📊 Performance validation summary:")
        print(f"      • F0.5 Score: {val_f05:.4f} {'✅' if validation_results['f05_score'].get('meets_target') else '❌'}")
        print(f"      • Train-Val Gap: {final_gap:.4f} {'✅' if validation_results['train_val_gap'].get('meets_target') else '❌'}")
        print(f"      • Validation Loss: {val_loss:.4f} {'✅' if validation_results['validation_loss'].get('meets_target') else '❌'}")
        print(f"      • Convergence: {convergence_epoch} epochs {'✅' if validation_results['convergence'].get('meets_target') else '❌'}")
        print(f"      • Overall Status: {validation_results['overall_status'].upper()}")
        
        return validation_results
        
    except Exception as e:
        print(f"❌ Error validating performance targets: {e}")
        return {
            'overall_status': 'error',
            'error': str(e),
            'summary': {'total_metrics': 0, 'metrics_passed': 0, 'overall_score': 0.0}
        }

def analyze_training_convergence(history: Dict[str, List]) -> Dict[str, Any]:
    """
    Analyze training convergence patterns and provide insights.
    
    Args:
        history: Training history dictionary with losses and metrics
        
    Returns:
        Dictionary with convergence analysis
    """
    analysis = {
        'convergence_epoch': None,
        'stability_score': 0.0,
        'overfitting_detected': False,
        'recommendations': []
    }
    
    if not history or 'val_f05' not in history:
        return analysis
    
    val_f05_history = history['val_f05']
    train_f05_history = history.get('train_f05', [])
    
    if len(val_f05_history) < 10:
        return analysis
    
    # Detect convergence point (when validation score stabilizes)
    window_size = min(10, len(val_f05_history) // 4)
    if window_size >= 3:
        val_scores = np.array(val_f05_history)
        rolling_std = np.array([np.std(val_scores[max(0, i-window_size):i+1]) 
                               for i in range(len(val_scores))])
        
        # Find first point where rolling std is consistently low
        stability_threshold = 0.01
        stable_points = rolling_std < stability_threshold
        if np.any(stable_points):
            analysis['convergence_epoch'] = np.argmax(stable_points) + 1
            analysis['stability_score'] = 1.0 - np.mean(rolling_std[-window_size:])
    
    # Detect overfitting (training score much higher than validation)
    if len(train_f05_history) == len(val_f05_history):
        gaps = np.array(train_f05_history) - np.array(val_f05_history)
        recent_gap = np.mean(gaps[-min(10, len(gaps)):])
        
        if recent_gap > 0.1:  # 10% gap threshold
            analysis['overfitting_detected'] = True
            analysis['recommendations'].append("Strong overfitting detected - consider more regularization")
        elif recent_gap > 0.05:
            analysis['recommendations'].append("Mild overfitting detected - monitor validation performance")
    
    # Analyze learning curve trends
    if len(val_f05_history) >= 20:
        recent_trend = np.polyfit(range(len(val_f05_history[-20:])), val_f05_history[-20:], 1)[0]
        if recent_trend < -0.001:  # Declining trend
            analysis['recommendations'].append("Validation performance declining - consider early stopping")
        elif recent_trend < 0.0001:  # Flat trend
            analysis['recommendations'].append("Performance plateaued - consider learning rate adjustment")
    
    return analysis