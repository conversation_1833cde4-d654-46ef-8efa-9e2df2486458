# ============================================================================
# GNN-LSTM Financial Crisis Prediction System - Dependencies
# ============================================================================
# 
# This file specifies all dependencies for the enhanced GNN-LSTM financial 
# crisis prediction system with TSAP architecture, comprehensive data leakage 
# prevention, and robust performance validation.
#
# Installation: pip install -r requirements.txt
# Python Version: 3.8+ (recommended: 3.9-3.11)
# ============================================================================

# ──────────────────────────────────────────────────────────────────────────────
# Core Machine Learning & Deep Learning Libraries
# ──────────────────────────────────────────────────────────────────────────────

# PyTorch ecosystem with CUDA support
torch>=1.12.0,<2.1.0
torchvision>=0.13.0,<0.16.0
torchaudio>=0.12.0,<2.1.0

# PyTorch Geometric for graph neural networks
torch-geometric>=2.3.0,<2.5.0
torch-scatter>=2.1.0,<2.2.0
torch-sparse>=0.6.15,<0.7.0
torch-cluster>=1.6.0,<1.7.0
torch-spline-conv>=1.2.2,<1.3.0

# ──────────────────────────────────────────────────────────────────────────────
# Data Processing & Scientific Computing
# ──────────────────────────────────────────────────────────────────────────────

# Core numerical computing
numpy>=1.21.0,<1.25.0
pandas>=1.4.0,<2.1.0
scipy>=1.8.0,<1.12.0

# Machine learning framework
scikit-learn>=1.1.0,<1.4.0

# Statistical analysis
statsmodels>=0.13.0,<0.15.0

# ──────────────────────────────────────────────────────────────────────────────
# Hyperparameter Optimization
# ──────────────────────────────────────────────────────────────────────────────

# Optuna for advanced HPO
optuna>=3.1.0,<3.5.0

# ──────────────────────────────────────────────────────────────────────────────
# Data Visualization & Analysis
# ──────────────────────────────────────────────────────────────────────────────

# Core plotting libraries
matplotlib>=3.5.0,<3.8.0
seaborn>=0.11.0,<0.13.0

# Interactive plotting (optional)
plotly>=5.10.0,<5.18.0

# ──────────────────────────────────────────────────────────────────────────────
# Development & Code Quality Tools
# ──────────────────────────────────────────────────────────────────────────────

# Testing framework
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0,<5.0.0

# Code formatting and linting
black>=22.0.0,<24.0.0
flake8>=4.0.0,<7.0.0
isort>=5.10.0,<6.0.0

# Type checking
mypy>=0.991,<1.8.0

# ──────────────────────────────────────────────────────────────────────────────
# Performance & Profiling Tools
# ──────────────────────────────────────────────────────────────────────────────

# Memory profiling
memory-profiler>=0.60.0,<0.62.0

# Progress bars and utilities
tqdm>=4.64.0,<5.0.0

# ──────────────────────────────────────────────────────────────────────────────
# Platform-Specific Dependencies
# ──────────────────────────────────────────────────────────────────────────────

# Windows-specific packages for better compatibility
pywin32>=304; sys_platform == "win32"
wmi>=1.5.1; sys_platform == "win32"

# ──────────────────────────────────────────────────────────────────────────────
# Optional Dependencies for Enhanced Features
# ──────────────────────────────────────────────────────────────────────────────

# Jupyter notebook support (optional)
jupyter>=1.0.0,<2.0.0
ipykernel>=6.15.0,<7.0.0
ipywidgets>=7.7.0,<9.0.0

# Enhanced data processing (optional)
openpyxl>=3.0.10,<3.2.0  # Excel file support
xlrd>=2.0.1,<2.1.0       # Excel file reading

# Network analysis (optional for graph visualization)
networkx>=2.8.0,<3.2.0

# ──────────────────────────────────────────────────────────────────────────────
# Version Compatibility Notes
# ──────────────────────────────────────────────────────────────────────────────
#
# PyTorch Compatibility:
# - PyTorch 1.12+ required for stable torch-geometric support
# - PyTorch 2.0+ supported but with compatibility constraints
# - CUDA 11.7+ recommended for GPU acceleration
#
# Python Compatibility:
# - Python 3.8: Minimum supported version
# - Python 3.9-3.11: Recommended versions with full feature support
# - Python 3.12: Experimental support (some packages may lag)
#
# Platform Support:
# - Windows 10/11: Full support with platform-specific packages
# - Linux: Full support (Ubuntu 18.04+, CentOS 7+)
# - macOS: Full support (macOS 10.15+, including Apple Silicon M1/M2)
#
# GPU Support:
# - NVIDIA GPUs: Full CUDA support with appropriate PyTorch installation
# - AMD GPUs: Limited support via ROCm (experimental)
# - Apple Silicon: MPS backend support in PyTorch 1.12+
#
# Memory Requirements:
# - Minimum: 8GB RAM for small datasets
# - Recommended: 16GB+ RAM for production workloads
# - GPU: 6GB+ VRAM recommended for large graph processing
#
# ──────────────────────────────────────────────────────────────────────────────
# Installation Instructions
# ──────────────────────────────────────────────────────────────────────────────
#
# 1. Basic Installation:
#    pip install -r requirements.txt
#
# 2. GPU Installation (CUDA 11.7):
#    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu117
#    pip install -r requirements.txt
#
# 3. Development Installation:
#    pip install -r requirements.txt
#    pip install -e .  # If using setup.py
#
# 4. Conda Environment (recommended):
#    conda create -n gnn-lstm python=3.9
#    conda activate gnn-lstm
#    pip install -r requirements.txt
#
# ──────────────────────────────────────────────────────────────────────────────
# Troubleshooting Common Issues
# ──────────────────────────────────────────────────────────────────────────────
#
# Issue 1: torch-geometric installation fails
# Solution: Install PyTorch first, then torch-geometric
#   pip install torch torchvision torchaudio
#   pip install torch-geometric
#
# Issue 2: CUDA version mismatch
# Solution: Check CUDA version and install matching PyTorch
#   nvidia-smi  # Check CUDA version
#   # Install matching PyTorch from https://pytorch.org/get-started/locally/
#
# Issue 3: Memory errors during installation
# Solution: Install packages individually or use --no-cache-dir
#   pip install --no-cache-dir -r requirements.txt
#
# Issue 4: Windows permission errors
# Solution: Run as administrator or use --user flag
#   pip install --user -r requirements.txt
#
# ============================================================================ 